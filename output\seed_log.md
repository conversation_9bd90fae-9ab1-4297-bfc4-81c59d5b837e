Processing DSL script src/seeds/all/autotests_seed.groovy
Processing DSL script src/seeds/all/basic_jobs.groovy
   Processing branch: bf-anticheat
       Processing code_matrix...
       Processing frostbite_syncer_setup...
       Processing frostbite_syncer_setup...
       Processing data_matrix...
       Processing frosty_matrix...
       Processing shift_branch...
       Processing code_matrix linux64server final...
       Processing code_matrix tool release...
       Processing code_matrix win64game final...
       Processing code_matrix win64game retail...
       Processing code_matrix win64server final...
       Processing data_matrix [name:server]...
       Processing data_matrix [name:win64]...
       Processing frosty_job_matrix linuxserver [format:digital, config:final, region:ww, args:]...
       Processing frosty_job_matrix server [format:files, config:final, region:ww, args:]...
       Processing frosty_job_matrix win64 [format:digital, config:final, region:ww, args:]...
       Processing frosty_job_matrix win64 [format:digital, config:retail, region:ww, args:]...
       Processing shift_branch...
   Processing branch: CH1-code-dev
       Processing code_matrix...
       Processing code_nomaster_matrix...
       Processing code_stressbulkbuild_matrix (start job)...
       Processing frostbite_syncer_setup...
       Processing frostbite_syncer_setup...
       Processing data_matrix...
       Processing frosty_matrix...
       Processing shift_branch...
       Processing code_matrix win64game final...
       Processing code_matrix win64game release...
       Processing code_matrix win64game retail...
       Processing code_matrix win64game performance...
       Processing code_matrix tool [name:release, compile_unit_tests:true, run_unit_tests:true]...
       Processing code_matrix tool [name:deprecation-test, allow_failure:true, compile_unit_tests:true]...
       Processing code_matrix win64server final...
       Processing code_matrix win64server release...
       Processing code_matrix xbsx final...
       Processing code_matrix xbsx retail...
       Processing code_matrix xbsx release...
       Processing code_matrix xbsx performance...
       Processing code_matrix ps5 final...
       Processing code_matrix ps5 retail...
       Processing code_matrix ps5 release...
       Processing code_matrix ps5 performance...
       Processing code_matrix linux64server final...
       Processing code_matrix linux64 final...
       Processing code_nomaster_matrix [name:win64game, configs:[retail]] retail...
       Processing code_nomaster_matrix [name:win64server, configs:[final]] final...
       Processing code_nomaster_matrix [name:ps5, configs:[final]] final...
       Processing code_nomaster_matrix [name:xbsx, configs:[retail]] retail...
       Processing code_nomaster_matrix [name:linux64server, configs:[final]] final...
       Processing code_nomaster_matrix [name:tool, configs:[release]] release...
       Processing code_stressbulkbuild_matrix tool release...
       Processing data_matrix [name:win64]...
       Processing data_matrix [name:server]...
       Processing data_matrix [name:xbsx]...
       Processing data_matrix [name:ps5]...
       Processing data_matrix [name:linux64]...
       Processing data_matrix [name:validate-frosted, allow_failure:true, deployment_platform:false]...
       Processing frosty_job_matrix win64 [format:files, config:final, region:ww, args: --additional-configs performance]...
       Processing frosty_job_matrix win64 [format:files, config:performance, region:ww, args:]...
       Processing frosty_job_matrix win64 [format:files, config:release, region:ww, args:]...
       Processing frosty_job_matrix ps5 [format:files, config:final, region:dev, args: --additional-configs release]...
       Processing frosty_job_matrix ps5 [format:files, config:performance, region:dev, args:]...
       Processing frosty_job_matrix xbsx [format:files, config:final, region:ww, args: --additional-configs release]...
       Processing frosty_job_matrix xbsx [format:files, config:performance, region:ww, args:]...
       Processing frosty_job_matrix server [format:files, config:final, region:ww, args:]...
       Processing frosty_job_matrix linuxserver [format:digital, config:final, region:ww, args:]...
       Processing frosty_job_matrix linuxserver [format:files, config:final, region:ww, args:]...
       Processing frosty_job_matrix linux64 [format:files, config:final, region:ww, args:]...
       Processing shift_branch...
       Processing pipeline_determinism_test_matrix...
   Processing branch: CH1-content-dev-sanitizers
       Processing code_matrix...
       Processing frostbite_syncer_setup...
       Processing frostbite_syncer_setup...
       Processing data_matrix...
       Processing code_matrix tool [name:release, tool_targets:[pipeline]]...
       Processing code_matrix tool [name:release, compile_unit_tests:true, asan_enabled:true, custom_tag:asan, tool_targets:[pipeline, win64-dll]]...
       Processing code_matrix ps5 [name:final, compile_unit_tests:true, asan_enabled:true, custom_tag:asan]...
       Processing code_matrix xbsx [name:final, compile_unit_tests:true, asan_enabled:true, custom_tag:asan]...
       Processing code_matrix win64game [name:final, compile_unit_tests:true, asan_enabled:true, custom_tag:asan]...
       Processing code_matrix win64server [name:final, asan_enabled:true, custom_tag:asan]...
       Processing code_matrix linux64server [name:final, asan_enabled:true, custom_tag:asan]...
       Processing code_matrix ps5 [name:final, compile_unit_tests:true, ubsan_enabled:true, custom_tag:ubsan]...
       Processing data_matrix [name:server, custom_tag:asan]...
       Processing data_matrix [name:win64, custom_tag:asan]...
       Processing data_matrix [name:xbsx, custom_tag:asan]...
       Processing data_matrix [name:ps5, custom_tag:asan]...
       Processing data_matrix [name:validate-frosted, allow_failure:true, deployment_platform:false, custom_tag:asan]...
   Processing branch: CH1-content-dev-clean
       Processing code_matrix...
       Processing frostbite_syncer_setup...
       Processing frostbite_syncer_setup...
       Processing code_matrix win64game release...
       Processing code_matrix tool release...
       Processing code_matrix linuxserver release...
       Processing code_matrix xbsx release...
       Processing code_matrix ps5 release...
       Processing code_matrix linux64 release...
   Processing branch: CH1-content-dev
       Processing code_matrix...
       Processing code_nomaster_matrix...
       Processing code_stressbulkbuild_matrix (start job)...
       Processing frostbite_syncer_setup...
       Processing frostbite_syncer_setup...
       Processing data_matrix...
       Processing frosty_matrix...
       Processing shift_branch...
       Processing code_matrix win64game final...
       Processing code_matrix win64game release...
       Processing code_matrix win64game retail...
       Processing code_matrix win64game performance...
       Processing code_matrix tool [name:release, compile_unit_tests:true, run_unit_tests:true]...
       Processing code_matrix tool [name:deprecation-test, allow_failure:true, compile_unit_tests:true]...
       Processing code_matrix win64server final...
       Processing code_matrix win64server release...
       Processing code_matrix xbsx final...
       Processing code_matrix xbsx retail...
       Processing code_matrix xbsx release...
       Processing code_matrix xbsx performance...
       Processing code_matrix ps5 final...
       Processing code_matrix ps5 retail...
       Processing code_matrix ps5 release...
       Processing code_matrix ps5 performance...
       Processing code_matrix linux64server final...
       Processing code_matrix linux64 final...
       Processing code_nomaster_matrix [name:win64game, configs:[retail]] retail...
       Processing code_nomaster_matrix [name:win64server, configs:[final]] final...
       Processing code_nomaster_matrix [name:ps5, configs:[final]] final...
       Processing code_nomaster_matrix [name:xbsx, configs:[retail]] retail...
       Processing code_nomaster_matrix [name:linux64server, configs:[final]] final...
       Processing code_nomaster_matrix [name:tool, configs:[release]] release...
       Processing code_stressbulkbuild_matrix tool release...
       Processing data_matrix [name:win64, enlighten_bake_group:[[group_name:group_1, cron_string:H 13 * * 1-5, enlighten_bake:[[name:TestRange_ContentsLookDev_Gym, asset:Test/TestRanges/TestRange_ContentsLookDev_Gym/TestRange_ContentsLookDev_Gym, mode:submit, filter:[062d6b23-7d7a-46cf-8754-8173a05f975f], type:asset], [name:Enlighten_MP_BrooklynVTAR_HighEnd, asset:Test/TestRanges/Gym_EUS_Brooklyn_VTAR/Gym_EUS_Brooklyn_VTAR, mode:submit, filter:[bab487ba-6398-4995-bc8d-3948be2f1cb0], type:asset], [name:Enlighten_SP_Prologue_HighEnd, asset:Game/GlacierSP/Levels/DSUB_SP_Prologue/DSUB_SP_Prologue, mode:submit, filter:[31e648ac-8bf9-4d1a-b505-1c0f7c6a3e9a], type:asset], [name:Enlighten_SP_Prologue_LowEnd, asset:Game/GlacierSP/Levels/DSUB_SP_Prologue/DSUB_SP_Prologue, mode:submit, filter:[4ca79328-1eb6-4bee-8056-dfd226766d8a], type:asset], [name:Enlighten_SP_TankAssault_HighEnd, asset:Game/GlacierSP/Levels/DSUB_SP_TankAssault/DSUB_SP_TankAssault, mode:submit, filter:[02097fc9-fcc7-48ca-9ed5-702e70ffb9e6], type:asset], [name:Enlighten_SP_TankAssault_LowEnd, asset:Game/GlacierSP/Levels/DSUB_SP_TankAssault/DSUB_SP_TankAssault, mode:submit, filter:[76688f30-4677-4154-a834-1d2dd061e1a0], type:asset], [name:ZS_GlobalPrecomputeDesc_MP_Granite_01, asset:Game/GlacierGranite/Levels/MP_Granite/ZS_GlobalPrecomputeDesc_MP_Granite_01, mode:submit, type:ZoneStreamer]]], [group_name:group_2, cron_string:H 2 * * 1-5, enlighten_bake:[[name:Enlighten_MP_Abbasid_HighEnd, asset:Game/GlacierMP/Levels/MP_Abbasid/MP_Abbasid, mode:submit, filter:[e1845bb6-373d-42aa-8235-67e76ef6be5a], type:asset], [name:Enlighten_MP_Abbasid_LowEnd, asset:Game/GlacierMP/Levels/MP_Abbasid/MP_Abbasid, mode:submit, filter:[62606c4d-e6a7-4698-8767-7ceb1720c104], type:asset], [name:MP_Tungsten_HighEnd, asset:Game/GlacierMP/Levels/MP_Tungsten/MP_Tungsten, mode:submit, filter:[fc23b954-f8f3-4967-9de3-fcbe797b6ac1], type:asset], [name:MP_Tungsten_LowEnd, asset:Game/GlacierMP/Levels/MP_Tungsten/MP_Tungsten, mode:submit, filter:[b0977be4-8966-4bcb-90c1-94c6da164a8d], type:asset], [name:DSUB_SP_NightRaid_LowEnd, asset:Game/GlacierSP/Levels/DSUB_SP_Nightraid/DSUB_SP_Nightraid, mode:submit, filter:[f4e389e1-dd37-4e91-9ed1-be4efd063d30], type:asset], [name:DSUB_SP_NightRaid_HighEnd, asset:Game/GlacierSP/Levels/DSUB_SP_Nightraid/DSUB_SP_Nightraid, mode:submit, filter:[316e9104-6b68-4b98-b5b3-e56ae12154e6], type:asset], [name:SP_BrooklynProtect_Enlighten_HighEnd, asset:Game/GlacierSP/Levels/DSUB_SP_BrooklynProtect/DSUB_SP_BrooklynProtect, mode:submit, filter:[6696e0a5-3334-4116-95b5-9f4dfe50111f], type:asset], [name:SP_BrooklynProtect_Enlighten_LowEnd, asset:Game/GlacierSP/Levels/DSUB_SP_BrooklynProtect/DSUB_SP_BrooklynProtect, mode:submit, filter:[14520c72-9b92-4181-8f44-d85641554d03], type:asset], [name:DSUB_SP_Assault_LowEnd, asset:Game/GlacierSP/Levels/DSUB_SP_Assault/DSUB_SP_Assault, mode:submit, filter:[953eea3c-a2db-4bdb-9b5a-c70d2a9a4338], type:asset], [name:DSUB_SP_Assault_HighEnd, asset:Game/GlacierSP/Levels/DSUB_SP_Assault/DSUB_SP_Assault, mode:submit, filter:[127a6359-d98a-497a-86ce-5008edac3bd4], type:asset], [name:Enlighten_MP_Dumbo_HighEnd, asset:Game/GlacierMP/Levels/MP_Dumbo/MP_Dumbo, mode:submit, filter:[ca1a51f1-fe77-4184-9132-f2e642232483], type:asset], [name:Enlighten_MP_Dumbo_LowEnd, asset:Game/GlacierMP/Levels/MP_Dumbo/MP_Dumbo, mode:submit, filter:[91e73452-8c1a-49cc-b39f-72c5dcdcaf73], type:asset], [name:Enlighten_MP_Battery_HighEnd, asset:Game/GlacierMP/Levels/MP_Battery/MP_Battery, mode:submit, filter:[7a198a3c-7964-41c9-ab7b-732a7564fd14], type:asset], [name:Enlighten_MP_Battery_LowEnd, asset:Game/GlacierMP/Levels/MP_Battery/MP_Battery, mode:submit, filter:[514426d3-a2ed-423f-a346-29d1956ee6a7], type:asset], [name:Diorama_SP_NightRaid, asset:Game/Dev/Dioramas/SP/Diorama_SP_NightRaid/Diorama_SP_NightRaid, mode:submit, filter:[371d2406-032c-486e-b3ce-e581e3a5f1ec], type:asset], [name:SP_BrooklynAttack_Enlighten_LowEnd, asset:Game/GlacierSP/Levels/DSUB_SP_BrooklynAttack/DSUB_SP_BrooklynAttack, mode:submit, filter:[1e84efe6-e8fa-433d-902d-0555f257205d], type:asset], [name:SP_BrooklynAttack_Enlighten_HighEnd, asset:Game/GlacierSP/Levels/DSUB_SP_BrooklynAttack/DSUB_SP_BrooklynAttack, mode:submit, filter:[6eade36a-f638-4a32-ac79-d945f518afa9], type:asset], [name:MP_Aftermath_Enlighten_HighEnd, asset:Game/GlacierMP/Levels/MP_Aftermath/MP_Aftermath, mode:submit, filter:[50b3afc3-89b3-4ebe-a158-e29ca837fad4], type:asset], [name:MP_Aftermath_Enlighten_LowEnd, asset:Game/GlacierMP/Levels/MP_Aftermath/MP_Aftermath, mode:submit, filter:[83fddc93-3186-4db8-8581-c0229911dff7], type:asset], [name:MP_Dumbo, asset:Game/GlacierMP/Levels/MP_Dumbo/MP_Dumbo, mode:submit, filter:[65c5bd72-702c-4a1b-b6eb-eddc34cb9f70], type:asset], [name:SP_Drone_HighEnd, asset:Game/GlacierSP/Levels/DSUB_SP_Drone/DSUB_SP_Drone, mode:submit, filter:[c0733c1f-4ce3-4b6c-b91f-f0e15a779721], type:asset], [name:SP_Infiltration_HighEnd, asset:Game/GlacierSP/Levels/DSUB_SP_Infiltration/DSUB_SP_Infiltration, mode:submit, filter:[5587c8c3-e7c4-4a76-a3bf-2c27d2123eb5], type:asset], [name:SP_Infiltration_LowEnd, asset:Game/GlacierSP/Levels/DSUB_SP_Infiltration/DSUB_SP_Infiltration, mode:submit, filter:[cad58882-5cb4-4857-84df-2db6813668a0], type:asset], [name:SP_Drone_LowEnd, asset:Game/GlacierSP/Levels/DSUB_SP_Drone/DSUB_SP_Drone, mode:submit, filter:[0fce117f-f846-40c0-8112-a0e222e10d89], type:asset], [name:MP_FireStorm_Highend, asset:Game/GlacierMP/Levels/MP_FireStorm/MP_FireStorm, mode:submit, filter:[9b21314b-332d-423f-b1d5-9c1cd6fa6f89], type:asset], [name:MP_FireStorm_LowEnd, asset:Game/GlacierMP/Levels/MP_FireStorm/MP_FireStorm, mode:submit, filter:[17035908-5ec3-4fc5-bc81-00afccfb7aea], type:asset], [name:MP_Capstone_HighEnd, asset:Game/GlacierMP/Levels/MP_Capstone/MP_Capstone, mode:submit, filter:[9d4e5662-b2de-41b7-8721-25febc96018e], type:asset], [name:MP_Capstone_LowEnd, asset:Game/GlacierMP/Levels/MP_Capstone/MP_Capstone, mode:submit, filter:[ee4b1ced-b2fb-44c7-a288-9007d2f63cc6], type:asset], [name:MP_Limestone_HighEnd, asset:Game/GlacierMP/Levels/MP_Limestone/MP_Limestone, mode:submit, filter:[13127704-7a3f-4556-9c9b-db06b2b73bd8], type:asset], [name:MP_Limestone_LowEnd, asset:Game/GlacierMP/Levels/MP_Limestone/MP_Limestone, mode:submit, filter:[a2de539d-93bb-4b4c-a674-484fdeb43c1e], type:asset], [name:MP_Outskirts_HighEnd, asset:Game/GlacierMP/Levels/MP_Outskirts/MP_Outskirts, mode:submit, filter:[b7561ebb-c00d-47bd-bc2e-5810d24bdfd0], type:asset], [name:MP_Outskirts_LowEnd, asset:Game/GlacierMP/Levels/MP_Outskirts/MP_Outskirts, mode:submit, filter:[30df8f1f-93ec-4920-b019-f6e9c1b21306], type:asset], [name:DSUB_SP_Invasion_HighEnd, asset:Game/GlacierSP/Levels/SP_Invasion/DSUB_SP_Invasion, mode:submit, filter:[c1e45036-3387-4409-b051-3bc8bd826e83], type:asset], [name:DSUB_SP_Invasion_LowEnd, asset:Game/GlacierSP/Levels/SP_Invasion/DSUB_SP_Invasion, mode:submit, filter:[f1335152-f35e-4244-8d08-42265a9cad20], type:asset]]]]]...
       Processing data_matrix [name:ps5]...
       Processing data_matrix [name:xbsx]...
       Processing data_matrix [name:server, combine_platform:false]...
       Processing data_matrix [name:linux64, combine_platform:false]...
       Processing data_matrix [name:validate-frosted, allow_failure:true, deployment_platform:false]...
       Processing frosty_job_matrix win64 [format:files, config:final, region:ww, args: --additional-configs performance]...
       Processing frosty_job_matrix win64 [format:files, config:performance, region:ww, args:]...
       Processing frosty_job_matrix win64 [format:files, config:release, region:ww, args:]...
       Processing frosty_job_matrix win64 [format:steam_combine, config:final, region:ww, args:, allow_failure:true]...
       Processing frosty_job_matrix win64 [format:steam_combine, config:retail, region:ww, args:, allow_failure:true]...
       Processing frosty_job_matrix win64 [format:combine, config:final, region:ww, args:, allow_failure:true]...
       Processing frosty_job_matrix win64 [format:combine, config:retail, region:ww, args:, allow_failure:true]...
       Processing frosty_job_matrix ps5 [format:files, config:final, region:dev, args: --additional-configs release]...
       Processing frosty_job_matrix ps5 [format:files, config:performance, region:dev, args:]...
       Processing frosty_job_matrix ps5 [format:combine, config:final, region:ww, args:, allow_failure:true]...
       Processing frosty_job_matrix ps5 [format:combine, config:retail, region:ww, args:, allow_failure:true]...
       Processing frosty_job_matrix xbsx [format:files, config:final, region:ww, args: --additional-configs release]...
       Processing frosty_job_matrix xbsx [format:files, config:performance, region:ww, args:]...
       Processing frosty_job_matrix xbsx [format:combine, config:final, region:ww, args:, allow_failure:true]...
       Processing frosty_job_matrix xbsx [format:combine, config:retail, region:ww, args:, allow_failure:true]...
       Processing frosty_job_matrix server [format:digital, config:final, region:ww, args:]...
       Processing frosty_job_matrix server [format:files, config:final, region:ww, args:]...
       Processing frosty_job_matrix linuxserver [format:digital, config:final, region:ww, args:]...
       Processing frosty_job_matrix linuxserver [format:files, config:final, region:ww, args:]...
       Processing frosty_job_matrix linux64 [format:files, config:final, region:ww, args:]...
       Processing shift_branch...
       Processing pipeline_determinism_test_matrix...
   Processing branch: CH1-content-dev-cache-losangeles
       Processing code_matrix...
       Processing frostbite_syncer_setup...
       Processing frostbite_syncer_setup...
       Processing data_matrix...
       Processing code_matrix tool release...
       Processing data_matrix [name:win64]...
       Processing data_matrix [name:ps5]...
       Processing data_matrix [name:xbsx]...
       Processing data_matrix [name:validate-frosted]...
   Processing branch: CH1-content-dev-disc-build
       Processing frostbite_syncer_setup...
       Processing frostbite_syncer_setup...
       Processing patchdata_matrix...
       Processing patchfrosty_matrix...
       Processing patchdata_matrix [name:win64]...
       Processing patchdata_matrix [name:ps5]...
       Processing patchdata_matrix [name:xbsx]...
       Processing frosty_job_matrix win64 [format:combine, config:final, region:ww, args:]...
       Processing frosty_job_matrix ps5 [format:combine, config:final, region:ww, args:]...
       Processing frosty_job_matrix xbsx [format:combine, config:final, region:ww, args:]...
   Processing branch: CH1-content-dev-first-patch
       Processing frostbite_syncer_setup...
       Processing frostbite_syncer_setup...
       Processing patchdata_matrix...
       Processing patchfrosty_matrix...
       Processing patchdata_matrix [name:win64]...
       Processing patchdata_matrix [name:ps5]...
       Processing patchdata_matrix [name:xbsx]...
       Processing patchfrosty_matrix win64 [format:combine, config:final, region:ww, args:]...
       Processing patchfrosty_matrix ps5 [format:combine, config:final, region:ww, args:]...
       Processing patchfrosty_matrix xbsx [format:combine, config:final, region:ww, args:]...
   Processing branch: CH1-content-dev-metrics
       Processing code_matrix...
       Processing frostbite_syncer_setup...
       Processing frostbite_syncer_setup...
       Processing code_matrix win64game [name:final, dry_run_code:true, skip_symbols_to_symstore:true, fb_env_values:[fbenv.extratelemetry=gensln:Labels=gla.build.metrics,Win64Game_GenSln,Final,CL%code_changelist%,NoTests,NoSNDBS, fbenv.extratelemetry=buildsln:Labels=gla.build.metrics,Win64Game_BuildSln,Final,CL%code_changelist%,NoTests,NoSNDBS]]...
       Processing code_matrix win64game [name:final, custom_tag:sndbs, dry_run_code:true, skip_symbols_to_symstore:true, sndbs_enabled:true, fb_env_values:[fbenv.extratelemetry=gensln:Labels=gla.build.metrics,Win64Game_GenSln,Final,CL%code_changelist%,NoTests,UsesSNDBS, fbenv.extratelemetry=buildsln:Labels=gla.build.metrics,Win64Game_BuildSln,Final,CL%code_changelist%,NoTests,UsesSNDBS]]...
       Processing code_matrix win64game [name:performance, dry_run_code:true, skip_symbols_to_symstore:true, fb_env_values:[fbenv.extratelemetry=gensln:Labels=gla.build.metrics,Win64Game_GenSln,Performance,CL%code_changelist%,NoTests,NoSNDBS, fbenv.extratelemetry=buildsln:Labels=gla.build.metrics,Win64Game_BuildSln,Performance,CL%code_changelist%,NoTests,NoSNDBS]]...
       Processing code_matrix win64game [name:performance, custom_tag:sndbs, dry_run_code:true, skip_symbols_to_symstore:true, sndbs_enabled:true, fb_env_values:[fbenv.extratelemetry=gensln:Labels=gla.build.metrics,Win64Game_GenSln,Performance,CL%code_changelist%,NoTests,UsesSNDBS, fbenv.extratelemetry=buildsln:Labels=gla.build.metrics,Win64Game_BuildSln,Performance,CL%code_changelist%,NoTests,UsesSNDBS]]...
       Processing code_matrix win64game [name:retail, dry_run_code:true, skip_symbols_to_symstore:true, fb_env_values:[fbenv.extratelemetry=gensln:Labels=gla.build.metrics,Win64Game_GenSln,Retail,CL%code_changelist%,NoTests,NoSNDBS, fbenv.extratelemetry=buildsln:Labels=gla.build.metrics,Win64Game_BuildSln,Retail,CL%code_changelist%,NoTests,NoSNDBS]]...
       Processing code_matrix win64game [name:retail, custom_tag:sndbs, dry_run_code:true, skip_symbols_to_symstore:true, sndbs_enabled:true, fb_env_values:[fbenv.extratelemetry=gensln:Labels=gla.build.metrics,Win64Game_GenSln,Retail,CL%code_changelist%,NoTests,UsesSNDBS, fbenv.extratelemetry=buildsln:Labels=gla.build.metrics,Win64Game_BuildSln,Retail,CL%code_changelist%,NoTests,UsesSNDBS]]...
       Processing code_matrix ps5 [name:final, dry_run_code:true, skip_symbols_to_symstore:true, fb_env_values:[fbenv.extratelemetry=gensln:Labels=gla.build.metrics,PS5_GenSln,Final,CL%code_changelist%,NoTests,NoSNDBS, fbenv.extratelemetry=buildsln:Labels=gla.build.metrics,PS5_BuildSln,Final,CL%code_changelist%,NoTests,NoSNDBS]]...
       Processing code_matrix ps5 [name:final, custom_tag:sndbs, dry_run_code:true, skip_symbols_to_symstore:true, sndbs_enabled:true, fb_env_values:[fbenv.extratelemetry=gensln:Labels=gla.build.metrics,PS5_GenSln,Final,CL%code_changelist%,NoTests,UsesSNDBS, fbenv.extratelemetry=buildsln:Labels=gla.build.metrics,PS5_BuildSln,Final,CL%code_changelist%,NoTests,UsesSNDBS]]...
       Processing code_matrix ps5 [name:performance, dry_run_code:true, skip_symbols_to_symstore:true, fb_env_values:[fbenv.extratelemetry=gensln:Labels=gla.build.metrics,PS5_GenSln,Performance,CL%code_changelist%,NoTests,NoSNDBS, fbenv.extratelemetry=buildsln:Labels=gla.build.metrics,PS5_BuildSln,Performance,CL%code_changelist%,NoTests,NoSNDBS]]...
       Processing code_matrix ps5 [name:performance, custom_tag:sndbs, dry_run_code:true, skip_symbols_to_symstore:true, sndbs_enabled:true, fb_env_values:[fbenv.extratelemetry=gensln:Labels=gla.build.metrics,PS5_GenSln,Performance,CL%code_changelist%,NoTests,UsesSNDBS, fbenv.extratelemetry=buildsln:Labels=gla.build.metrics,PS5_BuildSln,Performance,CL%code_changelist%,NoTests,UsesSNDBS]]...
       Processing code_matrix ps5 [name:retail, dry_run_code:true, skip_symbols_to_symstore:true, fb_env_values:[fbenv.extratelemetry=gensln:Labels=gla.build.metrics,PS5_GenSln,Retail,CL%code_changelist%,NoTests,NoSNDBS, fbenv.extratelemetry=buildsln:Labels=gla.build.metrics,PS5_BuildSln,Retail,CL%code_changelist%,NoTests,NoSNDBS]]...
       Processing code_matrix ps5 [name:retail, custom_tag:sndbs, dry_run_code:true, skip_symbols_to_symstore:true, sndbs_enabled:true, fb_env_values:[fbenv.extratelemetry=gensln:Labels=gla.build.metrics,PS5_GenSln,Retail,CL%code_changelist%,NoTests,UsesSNDBS, fbenv.extratelemetry=buildsln:Labels=gla.build.metrics,PS5_BuildSln,Retail,CL%code_changelist%,NoTests,UsesSNDBS]]...
       Processing code_matrix xbsx [name:final, dry_run_code:true, skip_symbols_to_symstore:true, fb_env_values:[fbenv.extratelemetry=gensln:Labels=gla.build.metrics,XBSX_GenSln,Final,CL%code_changelist%,NoTests,NoSNDBS, fbenv.extratelemetry=buildsln:Labels=gla.build.metrics,XBSX_BuildSln,Final,CL%code_changelist%,NoTests,NoSNDBS]]...
       Processing code_matrix xbsx [name:final, custom_tag:sndbs, dry_run_code:true, skip_symbols_to_symstore:true, sndbs_enabled:true, fb_env_values:[fbenv.extratelemetry=gensln:Labels=gla.build.metrics,XBSX_GenSln,Final,CL%code_changelist%,NoTests,UsesSNDBS, fbenv.extratelemetry=buildsln:Labels=gla.build.metrics,XBSX_BuildSln,Final,CL%code_changelist%,NoTests,UsesSNDBS]]...
       Processing code_matrix xbsx [name:performance, dry_run_code:true, skip_symbols_to_symstore:true, fb_env_values:[fbenv.extratelemetry=gensln:Labels=gla.build.metrics,XBSX_GenSln,Performance,CL%code_changelist%,NoTests,NoSNDBS, fbenv.extratelemetry=buildsln:Labels=gla.build.metrics,XBSX_BuildSln,Performance,CL%code_changelist%,NoTests,NoSNDBS]]...
       Processing code_matrix xbsx [name:performance, custom_tag:sndbs, dry_run_code:true, skip_symbols_to_symstore:true, sndbs_enabled:true, fb_env_values:[fbenv.extratelemetry=gensln:Labels=gla.build.metrics,XBSX_GenSln,Performance,CL%code_changelist%,NoTests,UsesSNDBS, fbenv.extratelemetry=buildsln:Labels=gla.build.metrics,XBSX_BuildSln,Performance,CL%code_changelist%,NoTests,UsesSNDBS]]...
       Processing code_matrix xbsx [name:retail, dry_run_code:true, skip_symbols_to_symstore:true, fb_env_values:[fbenv.extratelemetry=gensln:Labels=gla.build.metrics,XBSX_GenSln,Retail,CL%code_changelist%,NoTests,NoSNDBS, fbenv.extratelemetry=buildsln:Labels=gla.build.metrics,XBSX_BuildSln,Retail,CL%code_changelist%,NoTests,NoSNDBS]]...
       Processing code_matrix xbsx [name:retail, custom_tag:sndbs, dry_run_code:true, skip_symbols_to_symstore:true, sndbs_enabled:true, fb_env_values:[fbenv.extratelemetry=gensln:Labels=gla.build.metrics,XBSX_GenSln,Retail,CL%code_changelist%,NoTests,UsesSNDBS, fbenv.extratelemetry=buildsln:Labels=gla.build.metrics,XBSX_BuildSln,Retail,CL%code_changelist%,NoTests,UsesSNDBS]]...
       Processing code_matrix linux64server [name:final, dry_run_code:true, skip_symbols_to_symstore:true, fb_env_values:[fbenv.extratelemetry=gensln:Labels=gla.build.metrics,Linux_GenSln,Final,CL%code_changelist%,NoTests,NoSNDBS, fbenv.extratelemetry=buildsln:Labels=gla.build.metrics,Linux_BuildSln,Final,CL%code_changelist%,NoTests,NoSNDBS]]...
       Processing code_matrix linux64server [name:final, custom_tag:sndbs, dry_run_code:true, skip_symbols_to_symstore:true, sndbs_enabled:true, fb_env_values:[fbenv.extratelemetry=gensln:Labels=gla.build.metrics,Linux_GenSln,Final,CL%code_changelist%,NoTests,UsesSNDBS, fbenv.extratelemetry=buildsln:Labels=gla.build.metrics,Linux_BuildSln,Final,CL%code_changelist%,NoTests,UsesSNDBS]]...
       Processing code_matrix tool [name:release, compile_unit_tests:true, fb_env_values:[fbenv.extratelemetry=gensln:Labels=gla.build.metrics,Tool_GenSln,Release,CL%code_changelist%,IncludesTests,NoSNDBS, fbenv.extratelemetry=buildsln:Labels=gla.build.metrics,Tool_BuildSln,Release,CL%code_changelist%,IncludesTests,NoSNDBS]]...
       Processing code_matrix tool [name:release, compile_unit_tests:true, custom_tag:sndbs, dry_run_code:true, skip_symbols_to_symstore:true, sndbs_enabled:true, fb_env_values:[fbenv.extratelemetry=gensln:Labels=gla.build.metrics,Tool_GenSln,Release,CL%code_changelist%,IncludesTests,UsesSNDBS, fbenv.extratelemetry=buildsln:Labels=gla.build.metrics,Tool_BuildSln,Release,CL%code_changelist%,IncludesTests,UsesSNDBS]]...
   Processing branch: CH1-content-dev-C1S2B1
       Processing frostbite_syncer_setup...
       Processing frostbite_syncer_setup...
       Processing frosty_matrix...
       Processing shift_branch...
       Processing frosty_job_matrix win64 [format:files, config:final, region:ww, args: --additional-configs performance]...
       Processing frosty_job_matrix win64 [format:files, config:performance, region:ww, args: --additional-configs final]...
       Processing frosty_job_matrix ps5 [format:files, config:final, region:dev, args: --additional-configs release]...
       Processing frosty_job_matrix ps5 [format:files, config:performance, region:dev, args: --additional-configs final]...
       Processing frosty_job_matrix xbsx [format:files, config:final, region:ww, args: --additional-configs release]...
       Processing frosty_job_matrix xbsx [format:files, config:performance, region:ww, args: --additional-configs final]...
       Processing frosty_job_matrix server [format:digital, config:final, region:ww, args:]...
       Processing frosty_job_matrix server [format:files, config:final, region:ww, args:]...
       Processing frosty_job_matrix linuxserver [format:digital, config:final, region:ww, args:]...
       Processing frosty_job_matrix linuxserver [format:files, config:final, region:ww, args:]...
       Processing shift_branch...
   Processing branch: CH1-SP-content-dev
       Processing code_matrix...
       Processing frostbite_syncer_setup...
       Processing frostbite_syncer_setup...
       Processing data_matrix...
       Processing code_matrix win64game final...
       Processing code_matrix win64game retail...
       Processing code_matrix win64game release...
       Processing code_matrix win64game performance...
       Processing code_matrix ps5 final...
       Processing code_matrix ps5 retail...
       Processing code_matrix ps5 release...
       Processing code_matrix ps5 performance...
       Processing code_matrix xbsx final...
       Processing code_matrix xbsx retail...
       Processing code_matrix xbsx release...
       Processing code_matrix xbsx performance...
       Processing code_matrix win64server final...
       Processing code_matrix win64server release...
       Processing code_matrix linux64server final...
       Processing code_matrix linux64 final...
       Processing code_matrix tool [name:release, compile_unit_tests:true, run_unit_tests:true]...
       Processing data_matrix [name:win64]...
       Processing data_matrix [name:ps5]...
       Processing data_matrix [name:xbsx]...
   Processing branch: CH1-SP-content-dev-disc-build
       Processing frostbite_syncer_setup...
       Processing frostbite_syncer_setup...
       Processing patchdata_matrix...
       Processing patchdata_matrix [name:win64]...
       Processing patchdata_matrix [name:ps5]...
       Processing patchdata_matrix [name:xbsx]...
   Processing branch: CH1-SP-content-dev-first-patch
       Processing frostbite_syncer_setup...
       Processing frostbite_syncer_setup...
       Processing patchdata_matrix...
       Processing patchdata_matrix [name:win64]...
       Processing patchdata_matrix [name:ps5]...
       Processing patchdata_matrix [name:xbsx]...
   Processing branch: CH1-to-trunk
       Processing code_matrix...
       Processing frostbite_syncer_setup...
       Processing frostbite_syncer_setup...
       Processing data_matrix...
       Processing frosty_matrix...
       Processing shift_branch...
       Processing code_matrix win64game final...
       Processing code_matrix win64game retail...
       Processing code_matrix win64game release...
       Processing code_matrix win64game performance...
       Processing code_matrix tool [name:release, compile_unit_tests:true, run_unit_tests:true]...
       Processing code_matrix win64server final...
       Processing code_matrix xbsx final...
       Processing code_matrix xbsx retail...
       Processing code_matrix xbsx release...
       Processing code_matrix xbsx performance...
       Processing code_matrix ps5 final...
       Processing code_matrix ps5 retail...
       Processing code_matrix ps5 release...
       Processing code_matrix ps5 performance...
       Processing code_matrix linux64server final...
       Processing code_matrix linux64 final...
       Processing data_matrix [name:win64]...
       Processing data_matrix [name:server]...
       Processing data_matrix [name:xbsx]...
       Processing data_matrix [name:ps5]...
       Processing data_matrix [name:linux64]...
       Processing data_matrix [name:validate-frosted, allow_failure:true, deployment_platform:false]...
       Processing frosty_job_matrix win64 [format:files, config:final, region:ww, args:]...
       Processing frosty_job_matrix win64 [format:files, config:performance, region:ww, args:]...
       Processing frosty_job_matrix ps5 [format:files, config:final, region:dev, args: --additional-configs release]...
       Processing frosty_job_matrix ps5 [format:files, config:performance, region:dev, args:]...
       Processing frosty_job_matrix xbsx [format:files, config:final, region:ww, args: --additional-configs release]...
       Processing frosty_job_matrix xbsx [format:files, config:performance, region:ww, args:]...
       Processing frosty_job_matrix server [format:files, config:final, region:ww, args:]...
       Processing frosty_job_matrix linuxserver [format:digital, config:final, region:ww, args:]...
       Processing frosty_job_matrix linuxserver [format:files, config:final, region:ww, args:]...
       Processing frosty_job_matrix linux64 [format:files, config:final, region:ww, args:]...
       Processing shift_branch...
       Processing pipeline_determinism_test_matrix...
   Processing branch: 2024_1_dev-bf-to-CH1
       Processing code_matrix...
       Processing frostbite_syncer_setup...
       Processing frostbite_syncer_setup...
       Processing data_matrix...
       Processing frosty_matrix...
       Processing code_matrix win64game final...
       Processing code_matrix win64game performance...
       Processing code_matrix win64trial final...
       Processing code_matrix tool [name:release, compile_unit_tests:true, run_unit_tests:true]...
       Processing code_matrix tool [name:deprecation-test, allow_failure:true, compile_unit_tests:true]...
       Processing code_matrix win64server final...
       Processing code_matrix xbsx final...
       Processing code_matrix xbsx release...
       Processing code_matrix xbsx performance...
       Processing code_matrix ps5 final...
       Processing code_matrix ps5 release...
       Processing code_matrix ps5 performance...
       Processing code_matrix linux64server final...
       Processing code_matrix linux64 final...
       Processing data_matrix [name:win64]...
       Processing data_matrix [name:server]...
       Processing data_matrix [name:xbsx]...
       Processing data_matrix [name:ps5]...
       Processing data_matrix [name:linux64]...
       Processing frosty_job_matrix win64 [format:files, config:final, region:ww, args:]...
       Processing frosty_job_matrix win64 [format:files, config:performance, region:ww, args:]...
       Processing frosty_job_matrix ps5 [format:files, config:final, region:dev, args: --additional-configs release]...
       Processing frosty_job_matrix ps5 [format:files, config:performance, region:dev, args:]...
       Processing frosty_job_matrix xbsx [format:files, config:final, region:ww, args: --additional-configs release]...
       Processing frosty_job_matrix xbsx [format:files, config:performance, region:ww, args:]...
       Processing frosty_job_matrix server [format:files, config:final, region:ww, args:]...
       Processing frosty_job_matrix linuxserver [format:digital, config:final, region:ww, args:]...
       Processing frosty_job_matrix linuxserver [format:files, config:final, region:ww, args:]...
       Processing frosty_job_matrix linux64 [format:files, config:final, region:ww, args:]...
       Processing shift_branch...
       Processing pipeline_determinism_test_matrix...
   Processing branch: CH1-marketing-dev
       Processing code_matrix...
       Processing frostbite_syncer_setup...
       Processing frostbite_syncer_setup...
       Processing data_matrix...
       Processing code_matrix tool release...
       Processing data_matrix win64...
   Processing branch: CH1-playtest
       Processing code_matrix...
       Processing frostbite_syncer_setup...
       Processing frostbite_syncer_setup...
       Processing data_matrix...
       Processing frosty_matrix...
       Processing shift_branch...
       Processing code_matrix win64game final...
       Processing code_matrix win64game performance...
       Processing code_matrix tool release...
       Processing code_matrix xbsx performance...
       Processing code_matrix ps5 performance...
       Processing code_matrix linux64server final...
       Processing data_matrix [name:win64]...
       Processing data_matrix [name:server]...
       Processing data_matrix [name:xbsx]...
       Processing data_matrix [name:ps5]...
       Processing frosty_job_matrix win64 [format:files, config:performance, region:ww, args:]...
       Processing frosty_job_matrix ps5 [format:files, config:performance, region:dev, args:]...
       Processing frosty_job_matrix xbsx [format:files, config:performance, region:ww, args:]...
       Processing frosty_job_matrix linuxserver [format:digital, config:final, region:ww, args:]...
       Processing shift_branch...
   Processing branch: CH1-playtest-gnt
       Processing frostbite_syncer_setup...
       Processing frostbite_syncer_setup...
       Processing frosty_matrix...
       Processing shift_branch...
       Processing frosty_job_matrix linuxserver [format:digital, config:final, region:playtest, args:]...
       Processing frosty_job_matrix win64 [format:files, config:performance, region:playtest, args: --additional-configs final]...
       Processing frosty_job_matrix win64 [format:steam, config:performance, region:ww, args:]...
       Processing frosty_job_matrix ps5 [format:files, config:performance, region:playtest, args: --additional-configs final, allow_failure:true]...
       Processing frosty_job_matrix xbsx [format:files, config:performance, region:playtest, args: --additional-configs final, allow_failure:true]...
       Processing shift_branch...
   Processing branch: CH1-playtest-gnt-na
       Processing frostbite_syncer_setup...
       Processing frostbite_syncer_setup...
       Processing frosty_matrix...
       Processing shift_branch...
       Processing frosty_job_matrix linuxserver [format:digital, config:final, region:playtest, args:]...
       Processing frosty_job_matrix win64 [format:files, config:performance, region:playtest, args: --additional-configs final]...
       Processing frosty_job_matrix ps5 [format:files, config:performance, region:playtest, args: --additional-configs final, allow_failure:true]...
       Processing frosty_job_matrix xbsx [format:files, config:performance, region:playtest, args: --additional-configs final, allow_failure:true]...
       Processing shift_branch...
   Processing branch: CH1-playtest-san
       Processing frostbite_syncer_setup...
       Processing frostbite_syncer_setup...
       Processing frosty_matrix...
       Processing shift_branch...
       Processing frosty_job_matrix linuxserver [format:digital, config:final, region:playtest, args:]...
       Processing frosty_job_matrix win64 [format:files, config:final, region:playtest, args: --additional-configs performance ]...
       Processing frosty_job_matrix win64 [format:steam, config:performance, region:ww, args:]...
       Processing frosty_job_matrix ps5 [format:files, config:performance, region:playtest, args:, allow_failure:true]...
       Processing frosty_job_matrix xbsx [format:files, config:performance, region:playtest, args:, allow_failure:true]...
       Processing shift_branch...
   Processing branch: CH1-playtest-san-s2
       Processing frostbite_syncer_setup...
       Processing frostbite_syncer_setup...
       Processing frosty_matrix...
       Processing shift_branch...
       Processing frosty_job_matrix linuxserver [format:digital, config:final, region:playtest, args:]...
       Processing frosty_job_matrix win64 [format:files, config:final, region:playtest, args: --additional-configs performance ]...
       Processing frosty_job_matrix ps5 [format:files, config:performance, region:playtest, args:, allow_failure:true]...
       Processing frosty_job_matrix xbsx [format:files, config:performance, region:playtest, args:, allow_failure:true]...
       Processing shift_branch...
   Processing branch: CH1-playtest-sp
       Processing frostbite_syncer_setup...
       Processing frostbite_syncer_setup...
       Processing frosty_matrix...
       Processing shift_branch...
       Processing frosty_job_matrix win64 [format:files, config:final, region:playtestsp, args: --additional-configs performance ]...
       Processing frosty_job_matrix win64 [format:steam, config:performance, region:ww, args:]...
       Processing frosty_job_matrix ps5 [format:files, config:performance, region:dev, args:]...
       Processing frosty_job_matrix xbsx [format:files, config:performance, region:ww, args:]...
       Processing shift_branch...
   Processing branch: CH1-playtest-stage
       Processing code_matrix...
       Processing frostbite_syncer_setup...
       Processing frostbite_syncer_setup...
       Processing data_matrix...
       Processing frosty_matrix...
       Processing shift_branch...
       Processing code_matrix win64game final...
       Processing code_matrix win64game performance...
       Processing code_matrix tool release...
       Processing code_matrix xbsx performance...
       Processing code_matrix ps5 performance...
       Processing code_matrix linux64server final...
       Processing data_matrix [name:win64]...
       Processing data_matrix [name:server]...
       Processing data_matrix [name:xbsx]...
       Processing data_matrix [name:ps5]...
       Processing frosty_job_matrix win64 [format:files, config:performance, region:ww, args:]...
       Processing frosty_job_matrix ps5 [format:files, config:performance, region:dev, args:]...
       Processing frosty_job_matrix xbsx [format:files, config:performance, region:ww, args:]...
       Processing frosty_job_matrix linuxserver [format:digital, config:final, region:ww, args:]...
       Processing shift_branch...
   Processing branch: task13
       Processing code_matrix...
       Processing frostbite_syncer_setup...
       Processing frostbite_syncer_setup...
       Processing data_matrix...
       Processing frosty_matrix...
       Processing code_matrix tool release...
       Processing code_matrix win64game release...
       Processing code_matrix win64game performance...
       Processing code_matrix win64game final...
       Processing code_matrix win64server final...
       Processing code_matrix win64server release...
       Processing code_matrix linux64server final...
       Processing code_matrix linux64server release...
       Processing data_matrix [name:win64]...
       Processing data_matrix [name:server]...
       Processing frosty_job_matrix win64 [format:files, config:performance, region:ww, args:]...
       Processing frosty_job_matrix win64 [format:files, config:release, region:ww, args:]...
       Processing frosty_job_matrix win64 [format:files, config:final, region:ww, args:]...
       Processing frosty_job_matrix server [format:files, config:final, region:ww, args:]...
       Processing frosty_job_matrix server [format:files, config:release, region:ww, args:]...
       Processing frosty_job_matrix linuxserver [format:files, config:final, region:ww, args:]...
       Processing frosty_job_matrix linuxserver [format:files, config:release, region:ww, args:]...
       Processing shift_branch...
   Processing branch: task4
       Processing code_matrix...
       Processing frostbite_syncer_setup...
       Processing frostbite_syncer_setup...
       Processing data_matrix...
       Processing frosty_matrix...
       Processing code_matrix win64game final...
       Processing code_matrix win64game performance...
       Processing code_matrix tool [name:release, compile_unit_tests:true, run_unit_tests:true]...
       Processing code_matrix xbsx final...
       Processing code_matrix xbsx performance...
       Processing code_matrix ps5 final...
       Processing code_matrix ps5 performance...
       Processing code_matrix win64server final...
       Processing code_matrix linux64server final...
       Processing data_matrix [name:win64]...
       Processing data_matrix [name:server]...
       Processing data_matrix [name:xbsx]...
       Processing data_matrix [name:ps5]...
       Processing data_matrix [name:linux64]...
       Processing frosty_job_matrix linuxserver [format:digital, config:final, region:playtest, args:]...
       Processing frosty_job_matrix win64 [format:files, config:performance, region:playtest, args: --additional-configs final]...
       Processing frosty_job_matrix ps5 [format:files, config:performance, region:playtest, args: --additional-configs final, allow_failure:true]...
       Processing frosty_job_matrix xbsx [format:files, config:performance, region:playtest, args: --additional-configs final, allow_failure:true]...
   Processing branch: task8
       Processing code_matrix...
       Processing frostbite_syncer_setup...
       Processing frostbite_syncer_setup...
       Processing data_matrix...
       Processing frosty_matrix...
       Processing shift_branch...
       Processing code_matrix win64game final...
       Processing code_matrix win64game performance...
       Processing code_matrix tool [name:release, compile_unit_tests:true, run_unit_tests:true]...
       Processing code_matrix xbsx final...
       Processing code_matrix xbsx performance...
       Processing code_matrix ps5 final...
       Processing code_matrix ps5 performance...
       Processing code_matrix win64server final...
       Processing code_matrix linux64server final...
       Processing data_matrix [name:win64]...
       Processing data_matrix [name:server]...
       Processing data_matrix [name:xbsx]...
       Processing data_matrix [name:ps5]...
       Processing data_matrix [name:linux64]...
       Processing frosty_job_matrix linuxserver [format:digital, config:final, region:playtest, args:]...
       Processing frosty_job_matrix win64 [format:files, config:performance, region:playtest, args: --additional-configs final]...
       Processing frosty_job_matrix ps5 [format:files, config:performance, region:playtest, args: --additional-configs final, allow_failure:true]...
       Processing frosty_job_matrix xbsx [format:files, config:performance, region:playtest, args: --additional-configs final, allow_failure:true]...
       Processing shift_branch...
   Processing branch: task2
       Processing code_matrix...
       Processing code_nomaster_matrix...
       Processing frostbite_syncer_setup...
       Processing frostbite_syncer_setup...
       Processing data_matrix...
       Processing frosty_matrix...
       Processing code_matrix win64game final...
       Processing code_matrix win64game release...
       Processing code_matrix win64game retail...
       Processing code_matrix win64game performance...
       Processing code_matrix tool [name:release, compile_unit_tests:true, run_unit_tests:true]...
       Processing code_matrix win64server final...
       Processing code_matrix xbsx final...
       Processing code_matrix xbsx retail...
       Processing code_matrix xbsx release...
       Processing code_matrix xbsx performance...
       Processing code_matrix ps5 final...
       Processing code_matrix ps5 retail...
       Processing code_matrix ps5 release...
       Processing code_matrix ps5 performance...
       Processing code_matrix linux64server final...
       Processing code_nomaster_matrix [name:ps5, configs:[final]] final...
       Processing code_nomaster_matrix [name:tool, configs:[release]] release...
       Processing data_matrix [name:win64]...
       Processing data_matrix [name:server]...
       Processing data_matrix [name:xbsx]...
       Processing data_matrix [name:ps5]...
       Processing data_matrix [name:linux64]...
       Processing data_matrix [name:validate-frosted, allow_failure:true, deployment_platform:false]...
       Processing frosty_job_matrix win64 [format:files, config:final, region:ww]...
       Processing frosty_job_matrix ps5 [format:digital, config:final, region:dev]...
       Processing frosty_job_matrix xbsx [format:digital, config:final, region:ww]...
       Processing frosty_job_matrix linuxserver [format:digital, config:final, region:ww]...
       Processing frosty_job_matrix server [format:files, config:final, region:ww]...
       Processing pipeline_determinism_test_matrix...
Processing DSL script src/seeds/all/cobra_jobs.groovy
Processing DSL script src/seeds/all/coverity_seed.groovy
   Processing branch: bf-anticheat
   Processing branch: CH1-code-dev
       Processing coverity start job
       Processing Coverity
   Processing branch: CH1-content-dev-sanitizers
   Processing branch: CH1-content-dev-clean
   Processing branch: CH1-content-dev
       Processing coverity start job
       Processing Coverity
   Processing branch: CH1-content-dev-cache-losangeles
   Processing branch: CH1-content-dev-disc-build
   Processing branch: CH1-content-dev-first-patch
   Processing branch: CH1-content-dev-metrics
   Processing branch: CH1-content-dev-C1S2B1
   Processing branch: CH1-SP-content-dev
   Processing branch: CH1-SP-content-dev-disc-build
   Processing branch: CH1-SP-content-dev-first-patch
   Processing branch: CH1-to-trunk
   Processing branch: 2024_1_dev-bf-to-CH1
   Processing branch: CH1-marketing-dev
   Processing branch: CH1-playtest
   Processing branch: CH1-playtest-gnt
   Processing branch: CH1-playtest-gnt-na
   Processing branch: CH1-playtest-san
   Processing branch: CH1-playtest-san-s2
   Processing branch: CH1-playtest-sp
   Processing branch: CH1-playtest-stage
   Processing branch: task13
   Processing branch: task4
   Processing branch: task8
   Processing branch: task2
Processing DSL script src/seeds/all/dvcs_seed.groovy
Processing DSL script src/seeds/all/game_tools_jobs.groovy
   Processing branch: bf-anticheat
   Processing branch: CH1-code-dev
       Processing gametool start job
       Processing icepick
       Processing frostbiteDatabaseUpgrader
       Processing frostyisotool
       Processing drone
       Processing framework
       Processing fbenv
   Processing branch: CH1-content-dev-sanitizers
   Processing branch: CH1-content-dev-clean
   Processing branch: CH1-content-dev
       Processing gametool start job
       Processing icepick
       Processing frostbiteDatabaseUpgrader
       Processing frostyisotool
       Processing drone
       Processing framework
       Processing fbenv
   Processing branch: CH1-content-dev-cache-losangeles
   Processing branch: CH1-content-dev-disc-build
   Processing branch: CH1-content-dev-first-patch
   Processing branch: CH1-content-dev-metrics
   Processing branch: CH1-content-dev-C1S2B1
   Processing branch: CH1-SP-content-dev
   Processing branch: CH1-SP-content-dev-disc-build
   Processing branch: CH1-SP-content-dev-first-patch
   Processing branch: CH1-to-trunk
       Processing gametool start job
       Processing icepick
       Processing frostbiteDatabaseUpgrader
       Processing frostyisotool
       Processing drone
       Processing framework
       Processing fbenv
   Processing branch: 2024_1_dev-bf-to-CH1
       Processing gametool start job
       Processing icepick
       Processing frostbiteDatabaseUpgrader
       Processing frostyisotool
       Processing drone
   Processing branch: CH1-marketing-dev
   Processing branch: CH1-playtest
   Processing branch: CH1-playtest-gnt
   Processing branch: CH1-playtest-gnt-na
   Processing branch: CH1-playtest-san
   Processing branch: CH1-playtest-san-s2
   Processing branch: CH1-playtest-sp
   Processing branch: CH1-playtest-stage
   Processing branch: task13
   Processing branch: task4
   Processing branch: task8
   Processing branch: task2
       Processing gametool start job
       Processing icepick
       Processing frostbiteDatabaseUpgrader
       Processing frostyisotool
       Processing drone
       Processing framework
       Processing fbenv
Processing DSL script src/seeds/all/integrations_seed.groovy
   Processing integration branches...
       Processing branch: CH1-code-dev_to_CH1-content-dev
       Processing branch: CH1-code-dev-to-task5
       Processing branch: CH1-content-dev-to-task8
       Processing branch: CH1-content-dev-to-CH1-code-dev-branch-guardian
       Processing branch: CH1-SP-content-dev-to-CH1-content-dev
       Processing branch: CH1-content-dev-to-CH1-SP-content-dev-branch-guardian
       Processing branch: CH1-to-trunk-to-trunk-content-dev-branch-guardian
       Processing branch: trunk-content-dev_to_CH1-to-trunk
       Processing branch: CH1-content-dev-to-CH1-to-trunk-branch-guardian
       Processing branch: CH1-content-dev-to-task2
   Processing copy branches...
       Processing branch: CH1-content-dev_to_CH1_stage
       Processing branch: CH1-SP-content-dev_to_CH1-content-dev
       Processing branch: CH1-content-dev_to_CH1-SP-content-dev
       Processing branch: CH1-stage_to_CH1-release
       Processing branch: CH1-content-dev_to_task4
       Processing branch: CH1-content-dev_to_task8
       Processing branch: CH1-content-dev_to-task2
   Processing feature branches...
Processing DSL script src/seeds/all/maintenance_seed.groovy
Processing DSL script src/seeds/all/preflight_seed.groovy
Processing DSL script src/seeds/all/test_jobs_seed.groovy
Processing DSL script src/seeds/all/util_jobs.groovy
   Processing branch: bf-anticheat
      Processing webexport_branch
      Processing dry_run_data
      Processing enable_lkg_p4_counters
      Processing separate_symbol_store_upload
   Processing branch: CH1-code-dev
      Processing custom-tests.start
      Processing webexport_branch
      Processing single_stream_smoke
      Processing dry_run_data
      Processing CH1-code-dev.shift.offsite_basic_drone_shifter.upload job
      Processing CH1-code-dev.shift.offsite_basic_drone_shifter.start
      Processing remote_masters_to_receive_code
      Processing enable_lkg_p4_counters
      Processing separate_symbol_store_upload
      Processing custom-tests.build
   Processing branch: CH1-content-dev-sanitizers
      Processing dry_run_data
      Processing enable_lkg_p4_counters
   Processing branch: CH1-content-dev-clean
      Processing dry_run_data
      Processing separate_symbol_store_upload
   Processing branch: CH1-content-dev
      Processing custom-tests.start
      Processing webexport_branch
      Processing upgrade_data_job
      Processing single_stream_smoke
      Processing dry_run_data
      Processing CH1-content-dev.shift.offsite_basic_drone_shifter.upload job
      Processing CH1-content-dev.shift.offsite_drone_shifter.upload job
      Processing CH1-content-dev.shift.offsite_basic_drone_shifter.start
      Processing CH1-content-dev.shift.offsite_drone_shifter.start
      Processing remote_masters_to_receive_code
      Processing remote_masters_to_receive_data
      Processing enable_lkg_p4_counters
      Processing separate_symbol_store_upload
      Processing custom-tests.build
   Processing branch: CH1-content-dev-cache-losangeles
      Processing dry_run_data
      Processing enable_lkg_p4_counters
   Processing branch: CH1-content-dev-disc-build
      Processing dry_run_data
      Processing store_regular_baseline_builds
   Processing branch: CH1-content-dev-first-patch
      Processing dry_run_data
   Processing branch: CH1-content-dev-metrics
      Processing dry_run_data
      Processing enable_lkg_p4_counters
   Processing branch: CH1-content-dev-C1S2B1
      Processing dry_run_data
   Processing branch: CH1-SP-content-dev
      Processing upgrade_data_job
      Processing single_stream_smoke
      Processing dry_run_data
      Processing enable_lkg_p4_counters
      Processing separate_symbol_store_upload
   Processing branch: CH1-SP-content-dev-disc-build
      Processing dry_run_data
      Processing store_regular_baseline_builds
   Processing branch: CH1-SP-content-dev-first-patch
      Processing dry_run_data
   Processing branch: CH1-to-trunk
      Processing webexport_branch
      Processing dry_run_data
      Processing enable_lkg_p4_counters
      Processing separate_symbol_store_upload
   Processing branch: 2024_1_dev-bf-to-CH1
      Processing dry_run_data
      Processing enable_lkg_p4_counters
      Processing separate_symbol_store_upload
   Processing branch: CH1-marketing-dev
      Processing dry_run_data
      Processing separate_symbol_store_upload
   Processing branch: CH1-playtest
      Processing dry_run_data
      Processing separate_symbol_store_upload
   Processing branch: CH1-playtest-gnt
      Processing dry_run_data
   Processing branch: CH1-playtest-gnt-na
      Processing dry_run_data
   Processing branch: CH1-playtest-san
      Processing dry_run_data
   Processing branch: CH1-playtest-san-s2
      Processing dry_run_data
   Processing branch: CH1-playtest-sp
      Processing dry_run_data
   Processing branch: CH1-playtest-stage
      Processing dry_run_data
      Processing separate_symbol_store_upload
   Processing branch: task13
      Processing dry_run_data
      Processing task13.shift.offsite_basic_drone_shifter.upload job
      Processing task13.shift.offsite_basic_drone_shifter.start
      Processing enable_lkg_p4_counters
      Processing separate_symbol_store_upload
   Processing branch: task4
      Processing dry_run_data
      Processing enable_lkg_p4_counters
      Processing separate_symbol_store_upload
   Processing branch: task8
      Processing dry_run_data
      Processing enable_lkg_p4_counters
      Processing separate_symbol_store_upload
   Processing branch: task2
      Processing dry_run_data
      Processing enable_lkg_p4_counters
      Processing separate_symbol_store_upload
Processing DSL script src/seeds/all/view_seed_autotest.groovy
Processing DSL script src/seeds/all/view_seed_jobpipeline.groovy
Processing DSL script src/seeds/all/view_seed_maintenance.groovy
Processing DSL script src/seeds/all/view_seed_preflight.groovy
Existing items:
    GeneratedJob{name='2024_1_dev-bf-to-CH1.bfdata.linux64'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.bfdata.ps5'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.bfdata.server'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.bfdata.win64'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.bfdata.xbsx'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.bilbo.move.DiceStockholm.start'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.bilbo.move.earo.start'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.bilbo.move.linux64.final.DiceStockholm'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.bilbo.move.linux64.final.earo'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.bilbo.move.linux64server.final.DiceStockholm'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.bilbo.move.linux64server.final.earo'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.bilbo.move.ps5.final.DiceStockholm'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.bilbo.move.ps5.final.earo'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.bilbo.move.ps5.performance.DiceStockholm'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.bilbo.move.ps5.performance.earo'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.bilbo.move.ps5.release.DiceStockholm'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.bilbo.move.ps5.release.earo'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.bilbo.move.tool.deprecation-test.DiceStockholm'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.bilbo.move.tool.deprecation-test.earo'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.bilbo.move.tool.release.DiceStockholm'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.bilbo.move.tool.release.earo'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.bilbo.move.win64game.final.DiceStockholm'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.bilbo.move.win64game.final.earo'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.bilbo.move.win64game.performance.DiceStockholm'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.bilbo.move.win64game.performance.earo'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.bilbo.move.win64server.final.DiceStockholm'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.bilbo.move.win64server.final.earo'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.bilbo.move.win64trial.final.DiceStockholm'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.bilbo.move.win64trial.final.earo'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.bilbo.move.xbsx.final.DiceStockholm'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.bilbo.move.xbsx.final.earo'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.bilbo.move.xbsx.performance.DiceStockholm'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.bilbo.move.xbsx.performance.earo'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.bilbo.move.xbsx.release.DiceStockholm'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.bilbo.move.xbsx.release.earo'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.bilbo.register-bfdata-dronebuild'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.bilbo.register.local'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.bilbo.register.remote.DiceStockholm'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.bilbo.register.remote.earo'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.code.check'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.code.copy-to-filer'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.code.linux64.final'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.code.linux64server.final'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.code.p4counterupdater'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.code.ps5.final'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.code.ps5.performance'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.code.ps5.release'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.code.start'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.code.tool.deprecation-test'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.code.tool.release'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.code.tool.release.copy-build-to-azure'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.code.win64game.final'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.code.win64game.performance'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.code.win64server.final'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.code.win64trial.final'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.code.xbsx.final'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.code.xbsx.performance'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.code.xbsx.release'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.data.p4cleancounterupdater'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.data.p4counterupdater'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.data.start'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.frosty.bfdata.linux64.files.ww.final'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.frosty.bfdata.linuxserver.digital.ww.final'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.frosty.bfdata.linuxserver.files.ww.final'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.frosty.bfdata.ps5.files.dev.final'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.frosty.bfdata.ps5.files.dev.performance'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.frosty.bfdata.server.files.ww.final'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.frosty.bfdata.win64.files.ww.final'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.frosty.bfdata.win64.files.ww.performance'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.frosty.bfdata.xbsx.files.ww.final'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.frosty.bfdata.xbsx.files.ww.performance'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.frosty.p4counterupdater'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.frosty.start'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.gametool.drone'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.gametool.frostbiteDatabaseUpgrader'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.gametool.frostyisotool'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.gametool.icepick'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.gametool.start'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.move_location_frosty.DiceStockholm.bfdata.linux64.files.ww.final'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.move_location_frosty.DiceStockholm.bfdata.linuxserver.digital.ww.final'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.move_location_frosty.DiceStockholm.bfdata.linuxserver.files.ww.final'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.move_location_frosty.DiceStockholm.bfdata.ps5.files.dev.final'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.move_location_frosty.DiceStockholm.bfdata.ps5.files.dev.performance'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.move_location_frosty.DiceStockholm.bfdata.server.files.ww.final'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.move_location_frosty.DiceStockholm.bfdata.win64.files.ww.final'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.move_location_frosty.DiceStockholm.bfdata.win64.files.ww.performance'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.move_location_frosty.DiceStockholm.bfdata.xbsx.files.ww.final'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.move_location_frosty.DiceStockholm.bfdata.xbsx.files.ww.performance'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.pipeline-determinism-test'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.pipeline-determinism-test.start'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.shift.upload'}
    GeneratedJob{name='2024_1_dev-bf-to-CH1.symbolStoreUpload'}
    GeneratedJob{name='agent.reboot'}
    GeneratedJob{name='autoMaintenance.agent'}
    GeneratedJob{name='avalanche_maintenance'}
    GeneratedJob{name='avalanche_maintenance.start'}
    GeneratedJob{name='avalanche_maintenance_azure'}
    GeneratedJob{name='avalanchecli_drop_all_dbs'}
    GeneratedJob{name='avalanchecli_drop_all_dbs.start'}
    GeneratedJob{name='avalanchecli_nuke'}
    GeneratedJob{name='avalanchecli_nuke.start'}
    GeneratedJob{name='avalanchecli_nuke_all'}
    GeneratedJob{name='azure.testagent.create'}
    GeneratedJob{name='bf-anticheat.bfdata.server'}
    GeneratedJob{name='bf-anticheat.bfdata.webexport.win64'}
    GeneratedJob{name='bf-anticheat.bfdata.win64'}
    GeneratedJob{name='bf-anticheat.bilbo.register-bfdata-dronebuild'}
    GeneratedJob{name='bf-anticheat.code.check'}
    GeneratedJob{name='bf-anticheat.code.copy-to-filer'}
    GeneratedJob{name='bf-anticheat.code.linux64server.final'}
    GeneratedJob{name='bf-anticheat.code.p4counterupdater'}
    GeneratedJob{name='bf-anticheat.code.start'}
    GeneratedJob{name='bf-anticheat.code.tool.release'}
    GeneratedJob{name='bf-anticheat.code.win64game.final'}
    GeneratedJob{name='bf-anticheat.code.win64game.retail'}
    GeneratedJob{name='bf-anticheat.code.win64server.final'}
    GeneratedJob{name='bf-anticheat.data.p4cleancounterupdater'}
    GeneratedJob{name='bf-anticheat.data.p4counterupdater'}
    GeneratedJob{name='bf-anticheat.data.start'}
    GeneratedJob{name='bf-anticheat.frosty.bfdata.linuxserver.digital.ww.final'}
    GeneratedJob{name='bf-anticheat.frosty.bfdata.server.files.ww.final'}
    GeneratedJob{name='bf-anticheat.frosty.bfdata.win64.digital.ww.final'}
    GeneratedJob{name='bf-anticheat.frosty.bfdata.win64.digital.ww.retail'}
    GeneratedJob{name='bf-anticheat.frosty.p4counterupdater'}
    GeneratedJob{name='bf-anticheat.frosty.start'}
    GeneratedJob{name='bf-anticheat.shift.start'}
    GeneratedJob{name='bf-anticheat.shift.upload'}
    GeneratedJob{name='bf-anticheat.symbolStoreUpload'}
    GeneratedJob{name='CH1-code-dev.bfdata.linux64'}
    GeneratedJob{name='CH1-code-dev.bfdata.ps5'}
    GeneratedJob{name='CH1-code-dev.bfdata.server'}
    GeneratedJob{name='CH1-code-dev.bfdata.validate-frosted'}
    GeneratedJob{name='CH1-code-dev.bfdata.webexport.win64'}
    GeneratedJob{name='CH1-code-dev.bfdata.win64'}
    GeneratedJob{name='CH1-code-dev.bfdata.xbsx'}
    GeneratedJob{name='CH1-code-dev.bilbo.move.Guildford.start'}
    GeneratedJob{name='CH1-code-dev.bilbo.move.linux64.final.Guildford'}
    GeneratedJob{name='CH1-code-dev.bilbo.move.linux64.final.Montreal'}
    GeneratedJob{name='CH1-code-dev.bilbo.move.linux64.final.RippleEffect'}
    GeneratedJob{name='CH1-code-dev.bilbo.move.linux64server.final.Guildford'}
    GeneratedJob{name='CH1-code-dev.bilbo.move.linux64server.final.Montreal'}
    GeneratedJob{name='CH1-code-dev.bilbo.move.linux64server.final.RippleEffect'}
    GeneratedJob{name='CH1-code-dev.bilbo.move.Montreal.start'}
    GeneratedJob{name='CH1-code-dev.bilbo.move.ps5.final.Guildford'}
    GeneratedJob{name='CH1-code-dev.bilbo.move.ps5.final.Montreal'}
    GeneratedJob{name='CH1-code-dev.bilbo.move.ps5.final.RippleEffect'}
    GeneratedJob{name='CH1-code-dev.bilbo.move.ps5.performance.Guildford'}
    GeneratedJob{name='CH1-code-dev.bilbo.move.ps5.performance.Montreal'}
    GeneratedJob{name='CH1-code-dev.bilbo.move.ps5.performance.RippleEffect'}
    GeneratedJob{name='CH1-code-dev.bilbo.move.ps5.release.Guildford'}
    GeneratedJob{name='CH1-code-dev.bilbo.move.ps5.release.Montreal'}
    GeneratedJob{name='CH1-code-dev.bilbo.move.ps5.release.RippleEffect'}
    GeneratedJob{name='CH1-code-dev.bilbo.move.RippleEffect.start'}
    GeneratedJob{name='CH1-code-dev.bilbo.move.tool.release.Guildford'}
    GeneratedJob{name='CH1-code-dev.bilbo.move.tool.release.Montreal'}
    GeneratedJob{name='CH1-code-dev.bilbo.move.tool.release.RippleEffect'}
    GeneratedJob{name='CH1-code-dev.bilbo.move.win64game.final.Guildford'}
    GeneratedJob{name='CH1-code-dev.bilbo.move.win64game.final.Montreal'}
    GeneratedJob{name='CH1-code-dev.bilbo.move.win64game.final.RippleEffect'}
    GeneratedJob{name='CH1-code-dev.bilbo.move.win64game.performance.Guildford'}
    GeneratedJob{name='CH1-code-dev.bilbo.move.win64game.performance.Montreal'}
    GeneratedJob{name='CH1-code-dev.bilbo.move.win64game.performance.RippleEffect'}
    GeneratedJob{name='CH1-code-dev.bilbo.move.win64game.release.Guildford'}
    GeneratedJob{name='CH1-code-dev.bilbo.move.win64game.release.Montreal'}
    GeneratedJob{name='CH1-code-dev.bilbo.move.win64game.release.RippleEffect'}
    GeneratedJob{name='CH1-code-dev.bilbo.move.win64server.final.Guildford'}
    GeneratedJob{name='CH1-code-dev.bilbo.move.win64server.final.Montreal'}
    GeneratedJob{name='CH1-code-dev.bilbo.move.win64server.final.RippleEffect'}
    GeneratedJob{name='CH1-code-dev.bilbo.move.win64server.release.Guildford'}
    GeneratedJob{name='CH1-code-dev.bilbo.move.win64server.release.Montreal'}
    GeneratedJob{name='CH1-code-dev.bilbo.move.win64server.release.RippleEffect'}
    GeneratedJob{name='CH1-code-dev.bilbo.move.xbsx.final.Guildford'}
    GeneratedJob{name='CH1-code-dev.bilbo.move.xbsx.final.Montreal'}
    GeneratedJob{name='CH1-code-dev.bilbo.move.xbsx.final.RippleEffect'}
    GeneratedJob{name='CH1-code-dev.bilbo.move.xbsx.performance.Guildford'}
    GeneratedJob{name='CH1-code-dev.bilbo.move.xbsx.performance.Montreal'}
    GeneratedJob{name='CH1-code-dev.bilbo.move.xbsx.performance.RippleEffect'}
    GeneratedJob{name='CH1-code-dev.bilbo.move.xbsx.release.Guildford'}
    GeneratedJob{name='CH1-code-dev.bilbo.move.xbsx.release.Montreal'}
    GeneratedJob{name='CH1-code-dev.bilbo.move.xbsx.release.RippleEffect'}
    GeneratedJob{name='CH1-code-dev.bilbo.register-bfdata-dronebuild'}
    GeneratedJob{name='CH1-code-dev.bilbo.register.local'}
    GeneratedJob{name='CH1-code-dev.bilbo.register.remote.Guildford'}
    GeneratedJob{name='CH1-code-dev.bilbo.register.remote.Montreal'}
    GeneratedJob{name='CH1-code-dev.bilbo.register.remote.RippleEffect'}
    GeneratedJob{name='CH1-code-dev.code.check'}
    GeneratedJob{name='CH1-code-dev.code.copy-to-filer'}
    GeneratedJob{name='CH1-code-dev.code.lastknowngood'}
    GeneratedJob{name='CH1-code-dev.code.linux64.final'}
    GeneratedJob{name='CH1-code-dev.code.linux64server.final'}
    GeneratedJob{name='CH1-code-dev.code.nomaster.linux64server.final'}
    GeneratedJob{name='CH1-code-dev.code.nomaster.ps5.final'}
    GeneratedJob{name='CH1-code-dev.code.nomaster.start'}
    GeneratedJob{name='CH1-code-dev.code.nomaster.tool.release'}
    GeneratedJob{name='CH1-code-dev.code.nomaster.win64game.retail'}
    GeneratedJob{name='CH1-code-dev.code.nomaster.win64server.final'}
    GeneratedJob{name='CH1-code-dev.code.nomaster.xbsx.retail'}
    GeneratedJob{name='CH1-code-dev.code.p4counterupdater'}
    GeneratedJob{name='CH1-code-dev.code.ps5.final'}
    GeneratedJob{name='CH1-code-dev.code.ps5.performance'}
    GeneratedJob{name='CH1-code-dev.code.ps5.release'}
    GeneratedJob{name='CH1-code-dev.code.ps5.retail'}
    GeneratedJob{name='CH1-code-dev.code.start'}
    GeneratedJob{name='CH1-code-dev.code.stressbulkbuild.start'}
    GeneratedJob{name='CH1-code-dev.code.stressbulkbuild.tool.release'}
    GeneratedJob{name='CH1-code-dev.code.tool.deprecation-test'}
    GeneratedJob{name='CH1-code-dev.code.tool.release'}
    GeneratedJob{name='CH1-code-dev.code.tool.release.copy-build-to-azure'}
    GeneratedJob{name='CH1-code-dev.code.win64game.final'}
    GeneratedJob{name='CH1-code-dev.code.win64game.performance'}
    GeneratedJob{name='CH1-code-dev.code.win64game.release'}
    GeneratedJob{name='CH1-code-dev.code.win64game.retail'}
    GeneratedJob{name='CH1-code-dev.code.win64server.final'}
    GeneratedJob{name='CH1-code-dev.code.win64server.release'}
    GeneratedJob{name='CH1-code-dev.code.xbsx.final'}
    GeneratedJob{name='CH1-code-dev.code.xbsx.performance'}
    GeneratedJob{name='CH1-code-dev.code.xbsx.release'}
    GeneratedJob{name='CH1-code-dev.code.xbsx.retail'}
    GeneratedJob{name='CH1-code-dev.coverity'}
    GeneratedJob{name='CH1-code-dev.coverity.start'}
    GeneratedJob{name='CH1-code-dev.custom-script.portal-make-sdk'}
    GeneratedJob{name='CH1-code-dev.custom-tests.build'}
    GeneratedJob{name='CH1-code-dev.custom-tests.start'}
    GeneratedJob{name='CH1-code-dev.data.p4cleancounterupdater'}
    GeneratedJob{name='CH1-code-dev.data.p4counterupdater'}
    GeneratedJob{name='CH1-code-dev.data.start'}
    GeneratedJob{name='CH1-code-dev.deployment-data.bfdata.linux64'}
    GeneratedJob{name='CH1-code-dev.deployment-data.bfdata.ps5'}
    GeneratedJob{name='CH1-code-dev.deployment-data.bfdata.server'}
    GeneratedJob{name='CH1-code-dev.deployment-data.bfdata.win64'}
    GeneratedJob{name='CH1-code-dev.deployment-data.bfdata.xbsx'}
    GeneratedJob{name='CH1-code-dev.deployment-data.start'}
    GeneratedJob{name='CH1-code-dev.frosty.bfdata.linux64.files.ww.final'}
    GeneratedJob{name='CH1-code-dev.frosty.bfdata.linuxserver.digital.ww.final'}
    GeneratedJob{name='CH1-code-dev.frosty.bfdata.linuxserver.files.ww.final'}
    GeneratedJob{name='CH1-code-dev.frosty.bfdata.ps5.files.dev.final'}
    GeneratedJob{name='CH1-code-dev.frosty.bfdata.ps5.files.dev.performance'}
    GeneratedJob{name='CH1-code-dev.frosty.bfdata.server.files.ww.final'}
    GeneratedJob{name='CH1-code-dev.frosty.bfdata.win64.files.ww.final'}
    GeneratedJob{name='CH1-code-dev.frosty.bfdata.win64.files.ww.performance'}
    GeneratedJob{name='CH1-code-dev.frosty.bfdata.win64.files.ww.release'}
    GeneratedJob{name='CH1-code-dev.frosty.bfdata.xbsx.files.ww.final'}
    GeneratedJob{name='CH1-code-dev.frosty.bfdata.xbsx.files.ww.performance'}
    GeneratedJob{name='CH1-code-dev.frosty.p4counterupdater'}
    GeneratedJob{name='CH1-code-dev.frosty.start'}
    GeneratedJob{name='CH1-code-dev.gametool.drone'}
    GeneratedJob{name='CH1-code-dev.gametool.fbenv'}
    GeneratedJob{name='CH1-code-dev.gametool.framework'}
    GeneratedJob{name='CH1-code-dev.gametool.frostbiteDatabaseUpgrader'}
    GeneratedJob{name='CH1-code-dev.gametool.frostyisotool'}
    GeneratedJob{name='CH1-code-dev.gametool.icepick'}
    GeneratedJob{name='CH1-code-dev.gametool.start'}
    GeneratedJob{name='CH1-code-dev.integrate-upgrade-to.CH1-content-dev'}
    GeneratedJob{name='CH1-code-dev.integrate-upgrade-to.CH1-content-dev.start'}
    GeneratedJob{name='CH1-code-dev.integrate-upgrade-to.task5'}
    GeneratedJob{name='CH1-code-dev.integrate-upgrade-to.task5.start'}
    GeneratedJob{name='CH1-code-dev.pipeline-determinism-test'}
    GeneratedJob{name='CH1-code-dev.pipeline-determinism-test.start'}
    GeneratedJob{name='CH1-code-dev.register.smoke'}
    GeneratedJob{name='CH1-code-dev.set_integration_changelist'}
    GeneratedJob{name='CH1-code-dev.shift.offsite_basic_drone_shifter.start'}
    GeneratedJob{name='CH1-code-dev.shift.offsite_basic_drone_shifter.upload'}
    GeneratedJob{name='CH1-code-dev.shift.start'}
    GeneratedJob{name='CH1-code-dev.shift.upload'}
    GeneratedJob{name='CH1-code-dev.spin.linux64.files.final.ww'}
    GeneratedJob{name='CH1-code-dev.spin.linuxserver.digital.final.ww'}
    GeneratedJob{name='CH1-code-dev.symbolStoreUpload'}
    GeneratedJob{name='CH1-content-dev-C1S2B1.bilbo.register-bfdata-dronebuild'}
    GeneratedJob{name='CH1-content-dev-C1S2B1.code.check'}
    GeneratedJob{name='CH1-content-dev-C1S2B1.code.copy-to-filer'}
    GeneratedJob{name='CH1-content-dev-C1S2B1.frosty.bfdata.linuxserver.digital.ww.final'}
    GeneratedJob{name='CH1-content-dev-C1S2B1.frosty.bfdata.linuxserver.files.ww.final'}
    GeneratedJob{name='CH1-content-dev-C1S2B1.frosty.bfdata.ps5.files.dev.final'}
    GeneratedJob{name='CH1-content-dev-C1S2B1.frosty.bfdata.ps5.files.dev.performance'}
    GeneratedJob{name='CH1-content-dev-C1S2B1.frosty.bfdata.server.digital.ww.final'}
    GeneratedJob{name='CH1-content-dev-C1S2B1.frosty.bfdata.server.files.ww.final'}
    GeneratedJob{name='CH1-content-dev-C1S2B1.frosty.bfdata.win64.files.ww.final'}
    GeneratedJob{name='CH1-content-dev-C1S2B1.frosty.bfdata.win64.files.ww.performance'}
    GeneratedJob{name='CH1-content-dev-C1S2B1.frosty.bfdata.xbsx.files.ww.final'}
    GeneratedJob{name='CH1-content-dev-C1S2B1.frosty.bfdata.xbsx.files.ww.performance'}
    GeneratedJob{name='CH1-content-dev-C1S2B1.frosty.start'}
    GeneratedJob{name='CH1-content-dev-C1S2B1.shift.start'}
    GeneratedJob{name='CH1-content-dev-C1S2B1.shift.upload'}
    GeneratedJob{name='CH1-content-dev-C1S2B1.spin.linuxserver.digital.final.ww'}
    GeneratedJob{name='CH1-content-dev-cache-losangeles.bfdata.ps5'}
    GeneratedJob{name='CH1-content-dev-cache-losangeles.bfdata.validate-frosted'}
    GeneratedJob{name='CH1-content-dev-cache-losangeles.bfdata.win64'}
    GeneratedJob{name='CH1-content-dev-cache-losangeles.bfdata.xbsx'}
    GeneratedJob{name='CH1-content-dev-cache-losangeles.bilbo.register-bfdata-dronebuild'}
    GeneratedJob{name='CH1-content-dev-cache-losangeles.code.check'}
    GeneratedJob{name='CH1-content-dev-cache-losangeles.code.copy-to-filer'}
    GeneratedJob{name='CH1-content-dev-cache-losangeles.code.p4counterupdater'}
    GeneratedJob{name='CH1-content-dev-cache-losangeles.code.start'}
    GeneratedJob{name='CH1-content-dev-cache-losangeles.code.tool.release'}
    GeneratedJob{name='CH1-content-dev-cache-losangeles.data.p4cleancounterupdater'}
    GeneratedJob{name='CH1-content-dev-cache-losangeles.data.p4counterupdater'}
    GeneratedJob{name='CH1-content-dev-cache-losangeles.data.start'}
    GeneratedJob{name='CH1-content-dev-cache-losangeles.frosty.p4counterupdater'}
    GeneratedJob{name='CH1-content-dev-clean.bilbo.register-bfdata-dronebuild'}
    GeneratedJob{name='CH1-content-dev-clean.code.check'}
    GeneratedJob{name='CH1-content-dev-clean.code.copy-to-filer'}
    GeneratedJob{name='CH1-content-dev-clean.code.linux64.release'}
    GeneratedJob{name='CH1-content-dev-clean.code.linuxserver.release'}
    GeneratedJob{name='CH1-content-dev-clean.code.ps5.release'}
    GeneratedJob{name='CH1-content-dev-clean.code.start'}
    GeneratedJob{name='CH1-content-dev-clean.code.tool.release'}
    GeneratedJob{name='CH1-content-dev-clean.code.win64game.release'}
    GeneratedJob{name='CH1-content-dev-clean.code.xbsx.release'}
    GeneratedJob{name='CH1-content-dev-clean.symbolStoreUpload'}
    GeneratedJob{name='CH1-content-dev-disc-build.bilbo.register-bfdata-dronebuild'}
    GeneratedJob{name='CH1-content-dev-disc-build.code.check'}
    GeneratedJob{name='CH1-content-dev-disc-build.code.copy-to-filer'}
    GeneratedJob{name='CH1-content-dev-disc-build.frosty.bfdata.ps5.combine.ww.final'}
    GeneratedJob{name='CH1-content-dev-disc-build.frosty.bfdata.win64.combine.ww.final'}
    GeneratedJob{name='CH1-content-dev-disc-build.frosty.bfdata.xbsx.combine.ww.final'}
    GeneratedJob{name='CH1-content-dev-disc-build.patchdata-combine.bfdata.ps5'}
    GeneratedJob{name='CH1-content-dev-disc-build.patchdata-combine.bfdata.win64'}
    GeneratedJob{name='CH1-content-dev-disc-build.patchdata-combine.bfdata.xbsx'}
    GeneratedJob{name='CH1-content-dev-disc-build.patchdata.start'}
    GeneratedJob{name='CH1-content-dev-disc-build.patchfrosty.start'}
    GeneratedJob{name='CH1-content-dev-disc-build.store_regular_baseline.start'}
    GeneratedJob{name='CH1-content-dev-disc-build.store_regular_baseline_builds'}
    GeneratedJob{name='CH1-content-dev-first-patch.bilbo.register-bfdata-dronebuild'}
    GeneratedJob{name='CH1-content-dev-first-patch.code.check'}
    GeneratedJob{name='CH1-content-dev-first-patch.code.copy-to-filer'}
    GeneratedJob{name='CH1-content-dev-first-patch.patchdata-combine.bfdata.ps5'}
    GeneratedJob{name='CH1-content-dev-first-patch.patchdata-combine.bfdata.win64'}
    GeneratedJob{name='CH1-content-dev-first-patch.patchdata-combine.bfdata.xbsx'}
    GeneratedJob{name='CH1-content-dev-first-patch.patchdata.start'}
    GeneratedJob{name='CH1-content-dev-first-patch.patchfrosty.bfdata.ps5.combine.ww.final'}
    GeneratedJob{name='CH1-content-dev-first-patch.patchfrosty.bfdata.win64.combine.ww.final'}
    GeneratedJob{name='CH1-content-dev-first-patch.patchfrosty.bfdata.xbsx.combine.ww.final'}
    GeneratedJob{name='CH1-content-dev-first-patch.patchfrosty.start'}
    GeneratedJob{name='CH1-content-dev-metrics.bilbo.register-bfdata-dronebuild'}
    GeneratedJob{name='CH1-content-dev-metrics.code.check'}
    GeneratedJob{name='CH1-content-dev-metrics.code.copy-to-filer'}
    GeneratedJob{name='CH1-content-dev-metrics.code.linux64server.final'}
    GeneratedJob{name='CH1-content-dev-metrics.code.linux64server.final.sndbs'}
    GeneratedJob{name='CH1-content-dev-metrics.code.p4counterupdater'}
    GeneratedJob{name='CH1-content-dev-metrics.code.ps5.final'}
    GeneratedJob{name='CH1-content-dev-metrics.code.ps5.final.sndbs'}
    GeneratedJob{name='CH1-content-dev-metrics.code.ps5.performance'}
    GeneratedJob{name='CH1-content-dev-metrics.code.ps5.performance.sndbs'}
    GeneratedJob{name='CH1-content-dev-metrics.code.ps5.retail'}
    GeneratedJob{name='CH1-content-dev-metrics.code.ps5.retail.sndbs'}
    GeneratedJob{name='CH1-content-dev-metrics.code.start'}
    GeneratedJob{name='CH1-content-dev-metrics.code.tool.release'}
    GeneratedJob{name='CH1-content-dev-metrics.code.tool.release.sndbs'}
    GeneratedJob{name='CH1-content-dev-metrics.code.win64game.final'}
    GeneratedJob{name='CH1-content-dev-metrics.code.win64game.final.sndbs'}
    GeneratedJob{name='CH1-content-dev-metrics.code.win64game.performance'}
    GeneratedJob{name='CH1-content-dev-metrics.code.win64game.performance.sndbs'}
    GeneratedJob{name='CH1-content-dev-metrics.code.win64game.retail'}
    GeneratedJob{name='CH1-content-dev-metrics.code.win64game.retail.sndbs'}
    GeneratedJob{name='CH1-content-dev-metrics.code.xbsx.final'}
    GeneratedJob{name='CH1-content-dev-metrics.code.xbsx.final.sndbs'}
    GeneratedJob{name='CH1-content-dev-metrics.code.xbsx.performance'}
    GeneratedJob{name='CH1-content-dev-metrics.code.xbsx.performance.sndbs'}
    GeneratedJob{name='CH1-content-dev-metrics.code.xbsx.retail'}
    GeneratedJob{name='CH1-content-dev-metrics.code.xbsx.retail.sndbs'}
    GeneratedJob{name='CH1-content-dev-metrics.data.p4cleancounterupdater'}
    GeneratedJob{name='CH1-content-dev-metrics.data.p4counterupdater'}
    GeneratedJob{name='CH1-content-dev-metrics.frosty.p4counterupdater'}
    GeneratedJob{name='CH1-content-dev-sanitizers.bfdata.ps5.asan'}
    GeneratedJob{name='CH1-content-dev-sanitizers.bfdata.server.asan'}
    GeneratedJob{name='CH1-content-dev-sanitizers.bfdata.validate-frosted.asan'}
    GeneratedJob{name='CH1-content-dev-sanitizers.bfdata.win64.asan'}
    GeneratedJob{name='CH1-content-dev-sanitizers.bfdata.xbsx.asan'}
    GeneratedJob{name='CH1-content-dev-sanitizers.bilbo.register-bfdata-dronebuild'}
    GeneratedJob{name='CH1-content-dev-sanitizers.code.check'}
    GeneratedJob{name='CH1-content-dev-sanitizers.code.copy-to-filer'}
    GeneratedJob{name='CH1-content-dev-sanitizers.code.linux64server.final.asan'}
    GeneratedJob{name='CH1-content-dev-sanitizers.code.p4counterupdater'}
    GeneratedJob{name='CH1-content-dev-sanitizers.code.ps5.final.asan'}
    GeneratedJob{name='CH1-content-dev-sanitizers.code.ps5.final.ubsan'}
    GeneratedJob{name='CH1-content-dev-sanitizers.code.start'}
    GeneratedJob{name='CH1-content-dev-sanitizers.code.tool.release'}
    GeneratedJob{name='CH1-content-dev-sanitizers.code.tool.release.asan'}
    GeneratedJob{name='CH1-content-dev-sanitizers.code.win64game.final.asan'}
    GeneratedJob{name='CH1-content-dev-sanitizers.code.win64server.final.asan'}
    GeneratedJob{name='CH1-content-dev-sanitizers.code.xbsx.final.asan'}
    GeneratedJob{name='CH1-content-dev-sanitizers.data.p4cleancounterupdater'}
    GeneratedJob{name='CH1-content-dev-sanitizers.data.p4counterupdater'}
    GeneratedJob{name='CH1-content-dev-sanitizers.data.start'}
    GeneratedJob{name='CH1-content-dev-sanitizers.frosty.p4counterupdater'}
    GeneratedJob{name='CH1-content-dev.autotest-to-integration.code'}
    GeneratedJob{name='CH1-content-dev.bfdata.linux64'}
    GeneratedJob{name='CH1-content-dev.bfdata.ps5'}
    GeneratedJob{name='CH1-content-dev.bfdata.server'}
    GeneratedJob{name='CH1-content-dev.bfdata.upgrade.data'}
    GeneratedJob{name='CH1-content-dev.bfdata.validate-frosted'}
    GeneratedJob{name='CH1-content-dev.bfdata.webexport.win64'}
    GeneratedJob{name='CH1-content-dev.bfdata.win64'}
    GeneratedJob{name='CH1-content-dev.bfdata.xbsx'}
    GeneratedJob{name='CH1-content-dev.bilbo.move.earo.start'}
    GeneratedJob{name='CH1-content-dev.bilbo.move.Guildford.start'}
    GeneratedJob{name='CH1-content-dev.bilbo.move.linux64.final.earo'}
    GeneratedJob{name='CH1-content-dev.bilbo.move.linux64.final.Guildford'}
    GeneratedJob{name='CH1-content-dev.bilbo.move.linux64.final.Montreal'}
    GeneratedJob{name='CH1-content-dev.bilbo.move.linux64.final.RippleEffect'}
    GeneratedJob{name='CH1-content-dev.bilbo.move.linux64server.final.earo'}
    GeneratedJob{name='CH1-content-dev.bilbo.move.linux64server.final.Guildford'}
    GeneratedJob{name='CH1-content-dev.bilbo.move.linux64server.final.Montreal'}
    GeneratedJob{name='CH1-content-dev.bilbo.move.linux64server.final.RippleEffect'}
    GeneratedJob{name='CH1-content-dev.bilbo.move.Montreal.start'}
    GeneratedJob{name='CH1-content-dev.bilbo.move.ps5.final.earo'}
    GeneratedJob{name='CH1-content-dev.bilbo.move.ps5.final.Guildford'}
    GeneratedJob{name='CH1-content-dev.bilbo.move.ps5.final.Montreal'}
    GeneratedJob{name='CH1-content-dev.bilbo.move.ps5.final.RippleEffect'}
    GeneratedJob{name='CH1-content-dev.bilbo.move.ps5.performance.earo'}
    GeneratedJob{name='CH1-content-dev.bilbo.move.ps5.performance.Guildford'}
    GeneratedJob{name='CH1-content-dev.bilbo.move.ps5.performance.Montreal'}
    GeneratedJob{name='CH1-content-dev.bilbo.move.ps5.performance.RippleEffect'}
    GeneratedJob{name='CH1-content-dev.bilbo.move.ps5.release.earo'}
    GeneratedJob{name='CH1-content-dev.bilbo.move.ps5.release.Guildford'}
    GeneratedJob{name='CH1-content-dev.bilbo.move.ps5.release.Montreal'}
    GeneratedJob{name='CH1-content-dev.bilbo.move.ps5.release.RippleEffect'}
    GeneratedJob{name='CH1-content-dev.bilbo.move.RippleEffect.start'}
    GeneratedJob{name='CH1-content-dev.bilbo.move.tool.release.earo'}
    GeneratedJob{name='CH1-content-dev.bilbo.move.tool.release.Guildford'}
    GeneratedJob{name='CH1-content-dev.bilbo.move.tool.release.Montreal'}
    GeneratedJob{name='CH1-content-dev.bilbo.move.tool.release.RippleEffect'}
    GeneratedJob{name='CH1-content-dev.bilbo.move.win64game.final.earo'}
    GeneratedJob{name='CH1-content-dev.bilbo.move.win64game.final.Guildford'}
    GeneratedJob{name='CH1-content-dev.bilbo.move.win64game.final.Montreal'}
    GeneratedJob{name='CH1-content-dev.bilbo.move.win64game.final.RippleEffect'}
    GeneratedJob{name='CH1-content-dev.bilbo.move.win64game.performance.earo'}
    GeneratedJob{name='CH1-content-dev.bilbo.move.win64game.performance.Guildford'}
    GeneratedJob{name='CH1-content-dev.bilbo.move.win64game.performance.Montreal'}
    GeneratedJob{name='CH1-content-dev.bilbo.move.win64game.performance.RippleEffect'}
    GeneratedJob{name='CH1-content-dev.bilbo.move.win64game.release.earo'}
    GeneratedJob{name='CH1-content-dev.bilbo.move.win64game.release.Guildford'}
    GeneratedJob{name='CH1-content-dev.bilbo.move.win64game.release.Montreal'}
    GeneratedJob{name='CH1-content-dev.bilbo.move.win64game.release.RippleEffect'}
    GeneratedJob{name='CH1-content-dev.bilbo.move.win64server.final.earo'}
    GeneratedJob{name='CH1-content-dev.bilbo.move.win64server.final.Guildford'}
    GeneratedJob{name='CH1-content-dev.bilbo.move.win64server.final.Montreal'}
    GeneratedJob{name='CH1-content-dev.bilbo.move.win64server.final.RippleEffect'}
    GeneratedJob{name='CH1-content-dev.bilbo.move.win64server.release.earo'}
    GeneratedJob{name='CH1-content-dev.bilbo.move.win64server.release.Guildford'}
    GeneratedJob{name='CH1-content-dev.bilbo.move.win64server.release.Montreal'}
    GeneratedJob{name='CH1-content-dev.bilbo.move.win64server.release.RippleEffect'}
    GeneratedJob{name='CH1-content-dev.bilbo.move.xbsx.final.earo'}
    GeneratedJob{name='CH1-content-dev.bilbo.move.xbsx.final.Guildford'}
    GeneratedJob{name='CH1-content-dev.bilbo.move.xbsx.final.Montreal'}
    GeneratedJob{name='CH1-content-dev.bilbo.move.xbsx.final.RippleEffect'}
    GeneratedJob{name='CH1-content-dev.bilbo.move.xbsx.performance.earo'}
    GeneratedJob{name='CH1-content-dev.bilbo.move.xbsx.performance.Guildford'}
    GeneratedJob{name='CH1-content-dev.bilbo.move.xbsx.performance.Montreal'}
    GeneratedJob{name='CH1-content-dev.bilbo.move.xbsx.performance.RippleEffect'}
    GeneratedJob{name='CH1-content-dev.bilbo.move.xbsx.release.earo'}
    GeneratedJob{name='CH1-content-dev.bilbo.move.xbsx.release.Guildford'}
    GeneratedJob{name='CH1-content-dev.bilbo.move.xbsx.release.Montreal'}
    GeneratedJob{name='CH1-content-dev.bilbo.move.xbsx.release.RippleEffect'}
    GeneratedJob{name='CH1-content-dev.bilbo.register-bfdata-dronebuild'}
    GeneratedJob{name='CH1-content-dev.bilbo.register.local'}
    GeneratedJob{name='CH1-content-dev.bilbo.register.remote.earo'}
    GeneratedJob{name='CH1-content-dev.bilbo.register.remote.Guildford'}
    GeneratedJob{name='CH1-content-dev.bilbo.register.remote.Montreal'}
    GeneratedJob{name='CH1-content-dev.bilbo.register.remote.RippleEffect'}
    GeneratedJob{name='CH1-content-dev.code.check'}
    GeneratedJob{name='CH1-content-dev.code.copy-integrate-to.CH1-SP-content-dev'}
    GeneratedJob{name='CH1-content-dev.code.copy-to-filer'}
    GeneratedJob{name='CH1-content-dev.code.copy-to.CH1-SP-content-dev'}
    GeneratedJob{name='CH1-content-dev.code.copy-to.CH1-stage'}
    GeneratedJob{name='CH1-content-dev.code.copy-to.task2'}
    GeneratedJob{name='CH1-content-dev.code.copy-to.task4'}
    GeneratedJob{name='CH1-content-dev.code.copy-to.task8'}
    GeneratedJob{name='CH1-content-dev.code.lastknowngood'}
    GeneratedJob{name='CH1-content-dev.code.linux64.final'}
    GeneratedJob{name='CH1-content-dev.code.linux64server.final'}
    GeneratedJob{name='CH1-content-dev.code.merge-down.task2'}
    GeneratedJob{name='CH1-content-dev.code.merge-down.task8'}
    GeneratedJob{name='CH1-content-dev.code.nomaster.linux64server.final'}
    GeneratedJob{name='CH1-content-dev.code.nomaster.ps5.final'}
    GeneratedJob{name='CH1-content-dev.code.nomaster.start'}
    GeneratedJob{name='CH1-content-dev.code.nomaster.tool.release'}
    GeneratedJob{name='CH1-content-dev.code.nomaster.win64game.retail'}
    GeneratedJob{name='CH1-content-dev.code.nomaster.win64server.final'}
    GeneratedJob{name='CH1-content-dev.code.nomaster.xbsx.retail'}
    GeneratedJob{name='CH1-content-dev.code.p4counterupdater'}
    GeneratedJob{name='CH1-content-dev.code.ps5.final'}
    GeneratedJob{name='CH1-content-dev.code.ps5.performance'}
    GeneratedJob{name='CH1-content-dev.code.ps5.release'}
    GeneratedJob{name='CH1-content-dev.code.ps5.retail'}
    GeneratedJob{name='CH1-content-dev.code.start'}
    GeneratedJob{name='CH1-content-dev.code.stressbulkbuild.start'}
    GeneratedJob{name='CH1-content-dev.code.stressbulkbuild.tool.release'}
    GeneratedJob{name='CH1-content-dev.code.tool.deprecation-test'}
    GeneratedJob{name='CH1-content-dev.code.tool.release'}
    GeneratedJob{name='CH1-content-dev.code.tool.release.copy-build-to-azure'}
    GeneratedJob{name='CH1-content-dev.code.win64game.final'}
    GeneratedJob{name='CH1-content-dev.code.win64game.final.copy-build-to-azure'}
    GeneratedJob{name='CH1-content-dev.code.win64game.performance'}
    GeneratedJob{name='CH1-content-dev.code.win64game.release'}
    GeneratedJob{name='CH1-content-dev.code.win64game.retail'}
    GeneratedJob{name='CH1-content-dev.code.win64server.final'}
    GeneratedJob{name='CH1-content-dev.code.win64server.final.copy-build-to-azure'}
    GeneratedJob{name='CH1-content-dev.code.win64server.release'}
    GeneratedJob{name='CH1-content-dev.code.xbsx.final'}
    GeneratedJob{name='CH1-content-dev.code.xbsx.performance'}
    GeneratedJob{name='CH1-content-dev.code.xbsx.release'}
    GeneratedJob{name='CH1-content-dev.code.xbsx.retail'}
    GeneratedJob{name='CH1-content-dev.copy-integrate-to.CH1-SP-content-dev.start'}
    GeneratedJob{name='CH1-content-dev.coverity'}
    GeneratedJob{name='CH1-content-dev.coverity.start'}
    GeneratedJob{name='CH1-content-dev.custom-script.portal-make-sdk'}
    GeneratedJob{name='CH1-content-dev.custom-tests.build'}
    GeneratedJob{name='CH1-content-dev.custom-tests.start'}
    GeneratedJob{name='CH1-content-dev.data.lastknowngood'}
    GeneratedJob{name='CH1-content-dev.data.p4cleancounterupdater'}
    GeneratedJob{name='CH1-content-dev.data.p4counterupdater'}
    GeneratedJob{name='CH1-content-dev.data.start'}
    GeneratedJob{name='CH1-content-dev.deployment-data-combine.bfdata.ps5'}
    GeneratedJob{name='CH1-content-dev.deployment-data-combine.bfdata.win64'}
    GeneratedJob{name='CH1-content-dev.deployment-data-combine.bfdata.xbsx'}
    GeneratedJob{name='CH1-content-dev.deployment-data.bfdata.layer_C1S2B1.linux64'}
    GeneratedJob{name='CH1-content-dev.deployment-data.bfdata.layer_C1S2B1.ps5'}
    GeneratedJob{name='CH1-content-dev.deployment-data.bfdata.layer_C1S2B1.server'}
    GeneratedJob{name='CH1-content-dev.deployment-data.bfdata.layer_C1S2B1.win64'}
    GeneratedJob{name='CH1-content-dev.deployment-data.bfdata.layer_C1S2B1.xbsx'}
    GeneratedJob{name='CH1-content-dev.deployment-data.bfdata.linux64'}
    GeneratedJob{name='CH1-content-dev.deployment-data.bfdata.ps5'}
    GeneratedJob{name='CH1-content-dev.deployment-data.bfdata.server'}
    GeneratedJob{name='CH1-content-dev.deployment-data.bfdata.win64'}
    GeneratedJob{name='CH1-content-dev.deployment-data.bfdata.xbsx'}
    GeneratedJob{name='CH1-content-dev.deployment-data.start'}
    GeneratedJob{name='CH1-content-dev.enlighten.group_1.bfdata.Enlighten_MP_BrooklynVTAR_HighEnd'}
    GeneratedJob{name='CH1-content-dev.enlighten.group_1.bfdata.Enlighten_SP_Prologue_HighEnd'}
    GeneratedJob{name='CH1-content-dev.enlighten.group_1.bfdata.Enlighten_SP_Prologue_LowEnd'}
    GeneratedJob{name='CH1-content-dev.enlighten.group_1.bfdata.Enlighten_SP_TankAssault_HighEnd'}
    GeneratedJob{name='CH1-content-dev.enlighten.group_1.bfdata.Enlighten_SP_TankAssault_LowEnd'}
    GeneratedJob{name='CH1-content-dev.enlighten.group_1.bfdata.TestRange_ContentsLookDev_Gym'}
    GeneratedJob{name='CH1-content-dev.enlighten.group_1.bfdata.ZS_GlobalPrecomputeDesc_MP_Granite_01'}
    GeneratedJob{name='CH1-content-dev.enlighten.group_2.bfdata.Diorama_SP_NightRaid'}
    GeneratedJob{name='CH1-content-dev.enlighten.group_2.bfdata.DSUB_SP_Assault_HighEnd'}
    GeneratedJob{name='CH1-content-dev.enlighten.group_2.bfdata.DSUB_SP_Assault_LowEnd'}
    GeneratedJob{name='CH1-content-dev.enlighten.group_2.bfdata.DSUB_SP_Invasion_HighEnd'}
    GeneratedJob{name='CH1-content-dev.enlighten.group_2.bfdata.DSUB_SP_Invasion_LowEnd'}
    GeneratedJob{name='CH1-content-dev.enlighten.group_2.bfdata.DSUB_SP_NightRaid_HighEnd'}
    GeneratedJob{name='CH1-content-dev.enlighten.group_2.bfdata.DSUB_SP_NightRaid_LowEnd'}
    GeneratedJob{name='CH1-content-dev.enlighten.group_2.bfdata.Enlighten_MP_Abbasid_HighEnd'}
    GeneratedJob{name='CH1-content-dev.enlighten.group_2.bfdata.Enlighten_MP_Abbasid_LowEnd'}
    GeneratedJob{name='CH1-content-dev.enlighten.group_2.bfdata.Enlighten_MP_Battery_HighEnd'}
    GeneratedJob{name='CH1-content-dev.enlighten.group_2.bfdata.Enlighten_MP_Battery_LowEnd'}
    GeneratedJob{name='CH1-content-dev.enlighten.group_2.bfdata.Enlighten_MP_Dumbo_HighEnd'}
    GeneratedJob{name='CH1-content-dev.enlighten.group_2.bfdata.Enlighten_MP_Dumbo_LowEnd'}
    GeneratedJob{name='CH1-content-dev.enlighten.group_2.bfdata.MP_Aftermath_Enlighten_HighEnd'}
    GeneratedJob{name='CH1-content-dev.enlighten.group_2.bfdata.MP_Aftermath_Enlighten_LowEnd'}
    GeneratedJob{name='CH1-content-dev.enlighten.group_2.bfdata.MP_Capstone_HighEnd'}
    GeneratedJob{name='CH1-content-dev.enlighten.group_2.bfdata.MP_Capstone_LowEnd'}
    GeneratedJob{name='CH1-content-dev.enlighten.group_2.bfdata.MP_Dumbo'}
    GeneratedJob{name='CH1-content-dev.enlighten.group_2.bfdata.MP_FireStorm_Highend'}
    GeneratedJob{name='CH1-content-dev.enlighten.group_2.bfdata.MP_FireStorm_LowEnd'}
    GeneratedJob{name='CH1-content-dev.enlighten.group_2.bfdata.MP_Limestone_HighEnd'}
    GeneratedJob{name='CH1-content-dev.enlighten.group_2.bfdata.MP_Limestone_LowEnd'}
    GeneratedJob{name='CH1-content-dev.enlighten.group_2.bfdata.MP_Outskirts_HighEnd'}
    GeneratedJob{name='CH1-content-dev.enlighten.group_2.bfdata.MP_Outskirts_LowEnd'}
    GeneratedJob{name='CH1-content-dev.enlighten.group_2.bfdata.MP_Tungsten_HighEnd'}
    GeneratedJob{name='CH1-content-dev.enlighten.group_2.bfdata.MP_Tungsten_LowEnd'}
    GeneratedJob{name='CH1-content-dev.enlighten.group_2.bfdata.SP_BrooklynAttack_Enlighten_HighEnd'}
    GeneratedJob{name='CH1-content-dev.enlighten.group_2.bfdata.SP_BrooklynAttack_Enlighten_LowEnd'}
    GeneratedJob{name='CH1-content-dev.enlighten.group_2.bfdata.SP_BrooklynProtect_Enlighten_HighEnd'}
    GeneratedJob{name='CH1-content-dev.enlighten.group_2.bfdata.SP_BrooklynProtect_Enlighten_LowEnd'}
    GeneratedJob{name='CH1-content-dev.enlighten.group_2.bfdata.SP_Drone_HighEnd'}
    GeneratedJob{name='CH1-content-dev.enlighten.group_2.bfdata.SP_Drone_LowEnd'}
    GeneratedJob{name='CH1-content-dev.enlighten.group_2.bfdata.SP_Infiltration_HighEnd'}
    GeneratedJob{name='CH1-content-dev.enlighten.group_2.bfdata.SP_Infiltration_LowEnd'}
    GeneratedJob{name='CH1-content-dev.frosty.bfdata.linux64.files.ww.final'}
    GeneratedJob{name='CH1-content-dev.frosty.bfdata.linuxserver.digital.ww.final'}
    GeneratedJob{name='CH1-content-dev.frosty.bfdata.linuxserver.files.ww.final'}
    GeneratedJob{name='CH1-content-dev.frosty.bfdata.ps5.combine.ww.final'}
    GeneratedJob{name='CH1-content-dev.frosty.bfdata.ps5.combine.ww.retail'}
    GeneratedJob{name='CH1-content-dev.frosty.bfdata.ps5.files.dev.final'}
    GeneratedJob{name='CH1-content-dev.frosty.bfdata.ps5.files.dev.performance'}
    GeneratedJob{name='CH1-content-dev.frosty.bfdata.server.digital.ww.final'}
    GeneratedJob{name='CH1-content-dev.frosty.bfdata.server.files.ww.final'}
    GeneratedJob{name='CH1-content-dev.frosty.bfdata.win64.combine.ww.final'}
    GeneratedJob{name='CH1-content-dev.frosty.bfdata.win64.combine.ww.retail'}
    GeneratedJob{name='CH1-content-dev.frosty.bfdata.win64.files.ww.final'}
    GeneratedJob{name='CH1-content-dev.frosty.bfdata.win64.files.ww.performance'}
    GeneratedJob{name='CH1-content-dev.frosty.bfdata.win64.files.ww.release'}
    GeneratedJob{name='CH1-content-dev.frosty.bfdata.win64.steam_combine.ww.final'}
    GeneratedJob{name='CH1-content-dev.frosty.bfdata.win64.steam_combine.ww.retail'}
    GeneratedJob{name='CH1-content-dev.frosty.bfdata.xbsx.combine.ww.final'}
    GeneratedJob{name='CH1-content-dev.frosty.bfdata.xbsx.combine.ww.retail'}
    GeneratedJob{name='CH1-content-dev.frosty.bfdata.xbsx.files.ww.final'}
    GeneratedJob{name='CH1-content-dev.frosty.bfdata.xbsx.files.ww.performance'}
    GeneratedJob{name='CH1-content-dev.frosty.p4counterupdater'}
    GeneratedJob{name='CH1-content-dev.frosty.start'}
    GeneratedJob{name='CH1-content-dev.gametool.drone'}
    GeneratedJob{name='CH1-content-dev.gametool.fbenv'}
    GeneratedJob{name='CH1-content-dev.gametool.framework'}
    GeneratedJob{name='CH1-content-dev.gametool.frostbiteDatabaseUpgrader'}
    GeneratedJob{name='CH1-content-dev.gametool.frostyisotool'}
    GeneratedJob{name='CH1-content-dev.gametool.icepick'}
    GeneratedJob{name='CH1-content-dev.gametool.start'}
    GeneratedJob{name='CH1-content-dev.group_1.enlighten.start'}
    GeneratedJob{name='CH1-content-dev.group_2.enlighten.start'}
    GeneratedJob{name='CH1-content-dev.integrate-upgrade-to.CH1-code-dev'}
    GeneratedJob{name='CH1-content-dev.integrate-upgrade-to.CH1-to-trunk'}
    GeneratedJob{name='CH1-content-dev.integrate-upgrade-to.CH1-to-trunk.start'}
    GeneratedJob{name='CH1-content-dev.integrate-upgrade-to.upgrade.CH1-code-dev.start'}
    GeneratedJob{name='CH1-content-dev.pipeline-determinism-test'}
    GeneratedJob{name='CH1-content-dev.pipeline-determinism-test.start'}
    GeneratedJob{name='CH1-content-dev.register.smoke'}
    GeneratedJob{name='CH1-content-dev.set_integration_changelist'}
    GeneratedJob{name='CH1-content-dev.shift.offsite_basic_drone_shifter.start'}
    GeneratedJob{name='CH1-content-dev.shift.offsite_basic_drone_shifter.upload'}
    GeneratedJob{name='CH1-content-dev.shift.offsite_drone_shifter.start'}
    GeneratedJob{name='CH1-content-dev.shift.offsite_drone_shifter.upload'}
    GeneratedJob{name='CH1-content-dev.shift.start'}
    GeneratedJob{name='CH1-content-dev.shift.upload'}
    GeneratedJob{name='CH1-content-dev.spin.linux64.files.final.ww'}
    GeneratedJob{name='CH1-content-dev.spin.linuxserver.digital.final.ww'}
    GeneratedJob{name='CH1-content-dev.spin.mod-level-tools.na.na.na'}
    GeneratedJob{name='CH1-content-dev.symbolStoreUpload'}
    GeneratedJob{name='CH1-content-dev.win64.upload_to_steam.combine.ww.final'}
    GeneratedJob{name='CH1-content-dev.win64.upload_to_steam.combine.ww.retail'}
    GeneratedJob{name='CH1-marketing-dev.bfdata.win64'}
    GeneratedJob{name='CH1-marketing-dev.bilbo.move.Montreal.start'}
    GeneratedJob{name='CH1-marketing-dev.bilbo.move.RippleEffect.start'}
    GeneratedJob{name='CH1-marketing-dev.bilbo.move.tool.release.Montreal'}
    GeneratedJob{name='CH1-marketing-dev.bilbo.move.tool.release.RippleEffect'}
    GeneratedJob{name='CH1-marketing-dev.bilbo.register-bfdata-dronebuild'}
    GeneratedJob{name='CH1-marketing-dev.bilbo.register.local'}
    GeneratedJob{name='CH1-marketing-dev.bilbo.register.remote.Montreal'}
    GeneratedJob{name='CH1-marketing-dev.bilbo.register.remote.RippleEffect'}
    GeneratedJob{name='CH1-marketing-dev.code.check'}
    GeneratedJob{name='CH1-marketing-dev.code.copy-to-filer'}
    GeneratedJob{name='CH1-marketing-dev.code.start'}
    GeneratedJob{name='CH1-marketing-dev.code.tool.release'}
    GeneratedJob{name='CH1-marketing-dev.data.start'}
    GeneratedJob{name='CH1-marketing-dev.symbolStoreUpload'}
    GeneratedJob{name='CH1-playtest-gnt-na.bilbo.register-bfdata-dronebuild'}
    GeneratedJob{name='CH1-playtest-gnt-na.code.check'}
    GeneratedJob{name='CH1-playtest-gnt-na.code.copy-to-filer'}
    GeneratedJob{name='CH1-playtest-gnt-na.frosty.bfdata.linuxserver.digital.playtest.final'}
    GeneratedJob{name='CH1-playtest-gnt-na.frosty.bfdata.ps5.files.playtest.performance'}
    GeneratedJob{name='CH1-playtest-gnt-na.frosty.bfdata.win64.files.playtest.performance'}
    GeneratedJob{name='CH1-playtest-gnt-na.frosty.bfdata.xbsx.files.playtest.performance'}
    GeneratedJob{name='CH1-playtest-gnt-na.frosty.start'}
    GeneratedJob{name='CH1-playtest-gnt-na.shift.start'}
    GeneratedJob{name='CH1-playtest-gnt-na.shift.upload'}
    GeneratedJob{name='CH1-playtest-gnt-na.spin.linuxserver.digital.final.playtest'}
    GeneratedJob{name='CH1-playtest-gnt.bilbo.register-bfdata-dronebuild'}
    GeneratedJob{name='CH1-playtest-gnt.code.check'}
    GeneratedJob{name='CH1-playtest-gnt.code.copy-to-filer'}
    GeneratedJob{name='CH1-playtest-gnt.frosty.bfdata.linuxserver.digital.playtest.final'}
    GeneratedJob{name='CH1-playtest-gnt.frosty.bfdata.ps5.files.playtest.performance'}
    GeneratedJob{name='CH1-playtest-gnt.frosty.bfdata.win64.files.playtest.performance'}
    GeneratedJob{name='CH1-playtest-gnt.frosty.bfdata.win64.steam.ww.performance'}
    GeneratedJob{name='CH1-playtest-gnt.frosty.bfdata.xbsx.files.playtest.performance'}
    GeneratedJob{name='CH1-playtest-gnt.frosty.start'}
    GeneratedJob{name='CH1-playtest-gnt.shift.start'}
    GeneratedJob{name='CH1-playtest-gnt.shift.upload'}
    GeneratedJob{name='CH1-playtest-gnt.spin.linuxserver.digital.final.playtest'}
    GeneratedJob{name='CH1-playtest-gnt.win64.upload_to_steam.ww.performance'}
    GeneratedJob{name='CH1-playtest-san-s2.bilbo.register-bfdata-dronebuild'}
    GeneratedJob{name='CH1-playtest-san-s2.code.check'}
    GeneratedJob{name='CH1-playtest-san-s2.code.copy-to-filer'}
    GeneratedJob{name='CH1-playtest-san-s2.frosty.bfdata.linuxserver.digital.playtest.final'}
    GeneratedJob{name='CH1-playtest-san-s2.frosty.bfdata.ps5.files.playtest.performance'}
    GeneratedJob{name='CH1-playtest-san-s2.frosty.bfdata.win64.files.playtest.final'}
    GeneratedJob{name='CH1-playtest-san-s2.frosty.bfdata.xbsx.files.playtest.performance'}
    GeneratedJob{name='CH1-playtest-san-s2.frosty.start'}
    GeneratedJob{name='CH1-playtest-san-s2.shift.start'}
    GeneratedJob{name='CH1-playtest-san-s2.shift.upload'}
    GeneratedJob{name='CH1-playtest-san-s2.spin.linuxserver.digital.final.playtest'}
    GeneratedJob{name='CH1-playtest-san.bilbo.register-bfdata-dronebuild'}
    GeneratedJob{name='CH1-playtest-san.code.check'}
    GeneratedJob{name='CH1-playtest-san.code.copy-to-filer'}
    GeneratedJob{name='CH1-playtest-san.frosty.bfdata.linuxserver.digital.playtest.final'}
    GeneratedJob{name='CH1-playtest-san.frosty.bfdata.ps5.files.playtest.performance'}
    GeneratedJob{name='CH1-playtest-san.frosty.bfdata.win64.files.playtest.final'}
    GeneratedJob{name='CH1-playtest-san.frosty.bfdata.win64.steam.ww.performance'}
    GeneratedJob{name='CH1-playtest-san.frosty.bfdata.xbsx.files.playtest.performance'}
    GeneratedJob{name='CH1-playtest-san.frosty.start'}
    GeneratedJob{name='CH1-playtest-san.shift.start'}
    GeneratedJob{name='CH1-playtest-san.shift.upload'}
    GeneratedJob{name='CH1-playtest-san.spin.linuxserver.digital.final.playtest'}
    GeneratedJob{name='CH1-playtest-san.win64.upload_to_steam.ww.performance'}
    GeneratedJob{name='CH1-playtest-sp.bilbo.register-bfdata-dronebuild'}
    GeneratedJob{name='CH1-playtest-sp.code.check'}
    GeneratedJob{name='CH1-playtest-sp.code.copy-to-filer'}
    GeneratedJob{name='CH1-playtest-sp.frosty.bfdata.ps5.files.dev.performance'}
    GeneratedJob{name='CH1-playtest-sp.frosty.bfdata.win64.files.playtestsp.final'}
    GeneratedJob{name='CH1-playtest-sp.frosty.bfdata.win64.steam.ww.performance'}
    GeneratedJob{name='CH1-playtest-sp.frosty.bfdata.xbsx.files.ww.performance'}
    GeneratedJob{name='CH1-playtest-sp.frosty.start'}
    GeneratedJob{name='CH1-playtest-sp.shift.start'}
    GeneratedJob{name='CH1-playtest-sp.shift.upload'}
    GeneratedJob{name='CH1-playtest-sp.win64.upload_to_steam.ww.performance'}
    GeneratedJob{name='CH1-playtest-stage.bfdata.ps5'}
    GeneratedJob{name='CH1-playtest-stage.bfdata.server'}
    GeneratedJob{name='CH1-playtest-stage.bfdata.win64'}
    GeneratedJob{name='CH1-playtest-stage.bfdata.xbsx'}
    GeneratedJob{name='CH1-playtest-stage.bilbo.register-bfdata-dronebuild'}
    GeneratedJob{name='CH1-playtest-stage.code.check'}
    GeneratedJob{name='CH1-playtest-stage.code.copy-to-filer'}
    GeneratedJob{name='CH1-playtest-stage.code.linux64server.final'}
    GeneratedJob{name='CH1-playtest-stage.code.ps5.performance'}
    GeneratedJob{name='CH1-playtest-stage.code.start'}
    GeneratedJob{name='CH1-playtest-stage.code.tool.release'}
    GeneratedJob{name='CH1-playtest-stage.code.win64game.final'}
    GeneratedJob{name='CH1-playtest-stage.code.win64game.performance'}
    GeneratedJob{name='CH1-playtest-stage.code.xbsx.performance'}
    GeneratedJob{name='CH1-playtest-stage.data.start'}
    GeneratedJob{name='CH1-playtest-stage.frosty.bfdata.linuxserver.digital.ww.final'}
    GeneratedJob{name='CH1-playtest-stage.frosty.bfdata.ps5.files.dev.performance'}
    GeneratedJob{name='CH1-playtest-stage.frosty.bfdata.win64.files.ww.performance'}
    GeneratedJob{name='CH1-playtest-stage.frosty.bfdata.xbsx.files.ww.performance'}
    GeneratedJob{name='CH1-playtest-stage.frosty.start'}
    GeneratedJob{name='CH1-playtest-stage.shift.start'}
    GeneratedJob{name='CH1-playtest-stage.shift.upload'}
    GeneratedJob{name='CH1-playtest-stage.symbolStoreUpload'}
    GeneratedJob{name='CH1-playtest.bfdata.ps5'}
    GeneratedJob{name='CH1-playtest.bfdata.server'}
    GeneratedJob{name='CH1-playtest.bfdata.win64'}
    GeneratedJob{name='CH1-playtest.bfdata.xbsx'}
    GeneratedJob{name='CH1-playtest.bilbo.register-bfdata-dronebuild'}
    GeneratedJob{name='CH1-playtest.code.check'}
    GeneratedJob{name='CH1-playtest.code.copy-to-filer'}
    GeneratedJob{name='CH1-playtest.code.linux64server.final'}
    GeneratedJob{name='CH1-playtest.code.ps5.performance'}
    GeneratedJob{name='CH1-playtest.code.start'}
    GeneratedJob{name='CH1-playtest.code.tool.release'}
    GeneratedJob{name='CH1-playtest.code.win64game.final'}
    GeneratedJob{name='CH1-playtest.code.win64game.performance'}
    GeneratedJob{name='CH1-playtest.code.xbsx.performance'}
    GeneratedJob{name='CH1-playtest.data.start'}
    GeneratedJob{name='CH1-playtest.frosty.bfdata.linuxserver.digital.ww.final'}
    GeneratedJob{name='CH1-playtest.frosty.bfdata.ps5.files.dev.performance'}
    GeneratedJob{name='CH1-playtest.frosty.bfdata.win64.files.ww.performance'}
    GeneratedJob{name='CH1-playtest.frosty.bfdata.xbsx.files.ww.performance'}
    GeneratedJob{name='CH1-playtest.frosty.start'}
    GeneratedJob{name='CH1-playtest.shift.start'}
    GeneratedJob{name='CH1-playtest.shift.upload'}
    GeneratedJob{name='CH1-playtest.symbolStoreUpload'}
    GeneratedJob{name='CH1-SP-content-dev-disc-build.bilbo.register-bfdata-dronebuild'}
    GeneratedJob{name='CH1-SP-content-dev-disc-build.code.check'}
    GeneratedJob{name='CH1-SP-content-dev-disc-build.code.copy-to-filer'}
    GeneratedJob{name='CH1-SP-content-dev-disc-build.patchdata-combine.bfdata.ps5'}
    GeneratedJob{name='CH1-SP-content-dev-disc-build.patchdata-combine.bfdata.win64'}
    GeneratedJob{name='CH1-SP-content-dev-disc-build.patchdata-combine.bfdata.xbsx'}
    GeneratedJob{name='CH1-SP-content-dev-disc-build.patchdata.start'}
    GeneratedJob{name='CH1-SP-content-dev-disc-build.store_regular_baseline.start'}
    GeneratedJob{name='CH1-SP-content-dev-disc-build.store_regular_baseline_builds'}
    GeneratedJob{name='CH1-SP-content-dev-first-patch.bilbo.register-bfdata-dronebuild'}
    GeneratedJob{name='CH1-SP-content-dev-first-patch.code.check'}
    GeneratedJob{name='CH1-SP-content-dev-first-patch.code.copy-to-filer'}
    GeneratedJob{name='CH1-SP-content-dev-first-patch.patchdata-combine.bfdata.ps5'}
    GeneratedJob{name='CH1-SP-content-dev-first-patch.patchdata-combine.bfdata.win64'}
    GeneratedJob{name='CH1-SP-content-dev-first-patch.patchdata-combine.bfdata.xbsx'}
    GeneratedJob{name='CH1-SP-content-dev-first-patch.patchdata.start'}
    GeneratedJob{name='CH1-SP-content-dev.bfdata.ps5'}
    GeneratedJob{name='CH1-SP-content-dev.bfdata.upgrade.data'}
    GeneratedJob{name='CH1-SP-content-dev.bfdata.win64'}
    GeneratedJob{name='CH1-SP-content-dev.bfdata.xbsx'}
    GeneratedJob{name='CH1-SP-content-dev.bilbo.move.Guildford.start'}
    GeneratedJob{name='CH1-SP-content-dev.bilbo.move.linux64.final.Guildford'}
    GeneratedJob{name='CH1-SP-content-dev.bilbo.move.linux64.final.Montreal'}
    GeneratedJob{name='CH1-SP-content-dev.bilbo.move.linux64.final.RippleEffect'}
    GeneratedJob{name='CH1-SP-content-dev.bilbo.move.linux64server.final.Guildford'}
    GeneratedJob{name='CH1-SP-content-dev.bilbo.move.linux64server.final.Montreal'}
    GeneratedJob{name='CH1-SP-content-dev.bilbo.move.linux64server.final.RippleEffect'}
    GeneratedJob{name='CH1-SP-content-dev.bilbo.move.Montreal.start'}
    GeneratedJob{name='CH1-SP-content-dev.bilbo.move.ps5.final.Guildford'}
    GeneratedJob{name='CH1-SP-content-dev.bilbo.move.ps5.final.Montreal'}
    GeneratedJob{name='CH1-SP-content-dev.bilbo.move.ps5.final.RippleEffect'}
    GeneratedJob{name='CH1-SP-content-dev.bilbo.move.ps5.performance.Guildford'}
    GeneratedJob{name='CH1-SP-content-dev.bilbo.move.ps5.performance.Montreal'}
    GeneratedJob{name='CH1-SP-content-dev.bilbo.move.ps5.performance.RippleEffect'}
    GeneratedJob{name='CH1-SP-content-dev.bilbo.move.ps5.release.Guildford'}
    GeneratedJob{name='CH1-SP-content-dev.bilbo.move.ps5.release.Montreal'}
    GeneratedJob{name='CH1-SP-content-dev.bilbo.move.ps5.release.RippleEffect'}
    GeneratedJob{name='CH1-SP-content-dev.bilbo.move.RippleEffect.start'}
    GeneratedJob{name='CH1-SP-content-dev.bilbo.move.tool.release.Guildford'}
    GeneratedJob{name='CH1-SP-content-dev.bilbo.move.tool.release.Montreal'}
    GeneratedJob{name='CH1-SP-content-dev.bilbo.move.tool.release.RippleEffect'}
    GeneratedJob{name='CH1-SP-content-dev.bilbo.move.win64game.final.Guildford'}
    GeneratedJob{name='CH1-SP-content-dev.bilbo.move.win64game.final.Montreal'}
    GeneratedJob{name='CH1-SP-content-dev.bilbo.move.win64game.final.RippleEffect'}
    GeneratedJob{name='CH1-SP-content-dev.bilbo.move.win64game.performance.Guildford'}
    GeneratedJob{name='CH1-SP-content-dev.bilbo.move.win64game.performance.Montreal'}
    GeneratedJob{name='CH1-SP-content-dev.bilbo.move.win64game.performance.RippleEffect'}
    GeneratedJob{name='CH1-SP-content-dev.bilbo.move.win64game.release.Guildford'}
    GeneratedJob{name='CH1-SP-content-dev.bilbo.move.win64game.release.Montreal'}
    GeneratedJob{name='CH1-SP-content-dev.bilbo.move.win64game.release.RippleEffect'}
    GeneratedJob{name='CH1-SP-content-dev.bilbo.move.win64server.final.Guildford'}
    GeneratedJob{name='CH1-SP-content-dev.bilbo.move.win64server.final.Montreal'}
    GeneratedJob{name='CH1-SP-content-dev.bilbo.move.win64server.final.RippleEffect'}
    GeneratedJob{name='CH1-SP-content-dev.bilbo.move.win64server.release.Guildford'}
    GeneratedJob{name='CH1-SP-content-dev.bilbo.move.win64server.release.Montreal'}
    GeneratedJob{name='CH1-SP-content-dev.bilbo.move.win64server.release.RippleEffect'}
    GeneratedJob{name='CH1-SP-content-dev.bilbo.move.xbsx.final.Guildford'}
    GeneratedJob{name='CH1-SP-content-dev.bilbo.move.xbsx.final.Montreal'}
    GeneratedJob{name='CH1-SP-content-dev.bilbo.move.xbsx.final.RippleEffect'}
    GeneratedJob{name='CH1-SP-content-dev.bilbo.move.xbsx.performance.Guildford'}
    GeneratedJob{name='CH1-SP-content-dev.bilbo.move.xbsx.performance.Montreal'}
    GeneratedJob{name='CH1-SP-content-dev.bilbo.move.xbsx.performance.RippleEffect'}
    GeneratedJob{name='CH1-SP-content-dev.bilbo.move.xbsx.release.Guildford'}
    GeneratedJob{name='CH1-SP-content-dev.bilbo.move.xbsx.release.Montreal'}
    GeneratedJob{name='CH1-SP-content-dev.bilbo.move.xbsx.release.RippleEffect'}
    GeneratedJob{name='CH1-SP-content-dev.bilbo.register-bfdata-dronebuild'}
    GeneratedJob{name='CH1-SP-content-dev.bilbo.register.local'}
    GeneratedJob{name='CH1-SP-content-dev.bilbo.register.remote.Guildford'}
    GeneratedJob{name='CH1-SP-content-dev.bilbo.register.remote.Montreal'}
    GeneratedJob{name='CH1-SP-content-dev.bilbo.register.remote.RippleEffect'}
    GeneratedJob{name='CH1-SP-content-dev.code.check'}
    GeneratedJob{name='CH1-SP-content-dev.code.copy-to-filer'}
    GeneratedJob{name='CH1-SP-content-dev.code.copy-to.CH1-content-dev'}
    GeneratedJob{name='CH1-SP-content-dev.code.linux64.final'}
    GeneratedJob{name='CH1-SP-content-dev.code.linux64server.final'}
    GeneratedJob{name='CH1-SP-content-dev.code.p4counterupdater'}
    GeneratedJob{name='CH1-SP-content-dev.code.ps5.final'}
    GeneratedJob{name='CH1-SP-content-dev.code.ps5.performance'}
    GeneratedJob{name='CH1-SP-content-dev.code.ps5.release'}
    GeneratedJob{name='CH1-SP-content-dev.code.ps5.retail'}
    GeneratedJob{name='CH1-SP-content-dev.code.start'}
    GeneratedJob{name='CH1-SP-content-dev.code.tool.release'}
    GeneratedJob{name='CH1-SP-content-dev.code.win64game.final'}
    GeneratedJob{name='CH1-SP-content-dev.code.win64game.performance'}
    GeneratedJob{name='CH1-SP-content-dev.code.win64game.release'}
    GeneratedJob{name='CH1-SP-content-dev.code.win64game.retail'}
    GeneratedJob{name='CH1-SP-content-dev.code.win64server.final'}
    GeneratedJob{name='CH1-SP-content-dev.code.win64server.release'}
    GeneratedJob{name='CH1-SP-content-dev.code.xbsx.final'}
    GeneratedJob{name='CH1-SP-content-dev.code.xbsx.performance'}
    GeneratedJob{name='CH1-SP-content-dev.code.xbsx.release'}
    GeneratedJob{name='CH1-SP-content-dev.code.xbsx.retail'}
    GeneratedJob{name='CH1-SP-content-dev.data.p4cleancounterupdater'}
    GeneratedJob{name='CH1-SP-content-dev.data.p4counterupdater'}
    GeneratedJob{name='CH1-SP-content-dev.data.start'}
    GeneratedJob{name='CH1-SP-content-dev.data.start.register.verifiedForPreflight'}
    GeneratedJob{name='CH1-SP-content-dev.deployment-data.bfdata.ps5'}
    GeneratedJob{name='CH1-SP-content-dev.deployment-data.bfdata.win64'}
    GeneratedJob{name='CH1-SP-content-dev.deployment-data.bfdata.xbsx'}
    GeneratedJob{name='CH1-SP-content-dev.deployment-data.start'}
    GeneratedJob{name='CH1-SP-content-dev.frosty.p4counterupdater'}
    GeneratedJob{name='CH1-SP-content-dev.integrate-upgrade-to.CH1-content-dev'}
    GeneratedJob{name='CH1-SP-content-dev.integrate-upgrade-to.upgrade.CH1-content-dev.start'}
    GeneratedJob{name='CH1-SP-content-dev.register.smoke'}
    GeneratedJob{name='CH1-SP-content-dev.set_integration_changelist'}
    GeneratedJob{name='CH1-SP-content-dev.symbolStoreUpload'}
    GeneratedJob{name='CH1-stage.code.copy-to.CH1-release'}
    GeneratedJob{name='CH1-to-trunk.autotest-to-integration.code'}
    GeneratedJob{name='CH1-to-trunk.bfdata.linux64'}
    GeneratedJob{name='CH1-to-trunk.bfdata.ps5'}
    GeneratedJob{name='CH1-to-trunk.bfdata.server'}
    GeneratedJob{name='CH1-to-trunk.bfdata.validate-frosted'}
    GeneratedJob{name='CH1-to-trunk.bfdata.webexport.win64'}
    GeneratedJob{name='CH1-to-trunk.bfdata.win64'}
    GeneratedJob{name='CH1-to-trunk.bfdata.xbsx'}
    GeneratedJob{name='CH1-to-trunk.bilbo.register-bfdata-dronebuild'}
    GeneratedJob{name='CH1-to-trunk.code.check'}
    GeneratedJob{name='CH1-to-trunk.code.copy-to-filer'}
    GeneratedJob{name='CH1-to-trunk.code.linux64.final'}
    GeneratedJob{name='CH1-to-trunk.code.linux64server.final'}
    GeneratedJob{name='CH1-to-trunk.code.p4counterupdater'}
    GeneratedJob{name='CH1-to-trunk.code.ps5.final'}
    GeneratedJob{name='CH1-to-trunk.code.ps5.performance'}
    GeneratedJob{name='CH1-to-trunk.code.ps5.release'}
    GeneratedJob{name='CH1-to-trunk.code.ps5.retail'}
    GeneratedJob{name='CH1-to-trunk.code.start'}
    GeneratedJob{name='CH1-to-trunk.code.tool.release'}
    GeneratedJob{name='CH1-to-trunk.code.tool.release.copy-build-to-azure'}
    GeneratedJob{name='CH1-to-trunk.code.win64game.final'}
    GeneratedJob{name='CH1-to-trunk.code.win64game.performance'}
    GeneratedJob{name='CH1-to-trunk.code.win64game.release'}
    GeneratedJob{name='CH1-to-trunk.code.win64game.retail'}
    GeneratedJob{name='CH1-to-trunk.code.win64server.final'}
    GeneratedJob{name='CH1-to-trunk.code.xbsx.final'}
    GeneratedJob{name='CH1-to-trunk.code.xbsx.performance'}
    GeneratedJob{name='CH1-to-trunk.code.xbsx.release'}
    GeneratedJob{name='CH1-to-trunk.code.xbsx.retail'}
    GeneratedJob{name='CH1-to-trunk.data.p4cleancounterupdater'}
    GeneratedJob{name='CH1-to-trunk.data.p4counterupdater'}
    GeneratedJob{name='CH1-to-trunk.data.start'}
    GeneratedJob{name='CH1-to-trunk.frosty.bfdata.linux64.files.ww.final'}
    GeneratedJob{name='CH1-to-trunk.frosty.bfdata.linuxserver.digital.ww.final'}
    GeneratedJob{name='CH1-to-trunk.frosty.bfdata.linuxserver.files.ww.final'}
    GeneratedJob{name='CH1-to-trunk.frosty.bfdata.ps5.files.dev.final'}
    GeneratedJob{name='CH1-to-trunk.frosty.bfdata.ps5.files.dev.performance'}
    GeneratedJob{name='CH1-to-trunk.frosty.bfdata.server.files.ww.final'}
    GeneratedJob{name='CH1-to-trunk.frosty.bfdata.win64.files.ww.final'}
    GeneratedJob{name='CH1-to-trunk.frosty.bfdata.win64.files.ww.performance'}
    GeneratedJob{name='CH1-to-trunk.frosty.bfdata.xbsx.files.ww.final'}
    GeneratedJob{name='CH1-to-trunk.frosty.bfdata.xbsx.files.ww.performance'}
    GeneratedJob{name='CH1-to-trunk.frosty.p4counterupdater'}
    GeneratedJob{name='CH1-to-trunk.frosty.start'}
    GeneratedJob{name='CH1-to-trunk.gametool.drone'}
    GeneratedJob{name='CH1-to-trunk.gametool.fbenv'}
    GeneratedJob{name='CH1-to-trunk.gametool.framework'}
    GeneratedJob{name='CH1-to-trunk.gametool.frostbiteDatabaseUpgrader'}
    GeneratedJob{name='CH1-to-trunk.gametool.frostyisotool'}
    GeneratedJob{name='CH1-to-trunk.gametool.icepick'}
    GeneratedJob{name='CH1-to-trunk.gametool.start'}
    GeneratedJob{name='CH1-to-trunk.integrate-upgrade-to.trunk-content-dev'}
    GeneratedJob{name='CH1-to-trunk.integrate-upgrade-to.trunk-content-dev.start'}
    GeneratedJob{name='CH1-to-trunk.pipeline-determinism-test'}
    GeneratedJob{name='CH1-to-trunk.pipeline-determinism-test.start'}
    GeneratedJob{name='CH1-to-trunk.shift.start'}
    GeneratedJob{name='CH1-to-trunk.shift.upload'}
    GeneratedJob{name='CH1-to-trunk.spin.linuxserver.digital.final.ww'}
    GeneratedJob{name='CH1-to-trunk.symbolStoreUpload'}
    GeneratedJob{name='clean_agent_folder'}
    GeneratedJob{name='code.warm.cobra'}
    GeneratedJob{name='controller.delete_orphaned_jobs'}
    GeneratedJob{name='controller.job_monitoring'}
    GeneratedJob{name='controller.register'}
    GeneratedJob{name='controller.shutdown'}
    GeneratedJob{name='data.warm.cobra'}
    GeneratedJob{name='delete.agent'}
    GeneratedJob{name='delete.cloud.agent'}
    GeneratedJob{name='jenkins.cleanup'}
    GeneratedJob{name='jenkins.gc'}
    GeneratedJob{name='node-status-influxdb-cobra-etl'}
    GeneratedJob{name='offline.agent'}
    GeneratedJob{name='p4_clean_codestream.bct_build_criterion'}
    GeneratedJob{name='p4_clean_codestream.bct_build_dice'}
    GeneratedJob{name='p4_clean_codestream.bct_build_eala'}
    GeneratedJob{name='p4_clean_datastream.bct_build_criterion'}
    GeneratedJob{name='p4_clean_datastream.bct_build_dice'}
    GeneratedJob{name='p4_clean_datastream.bct_build_eala'}
    GeneratedJob{name='reapply-bfa-to-job'}
    GeneratedJob{name='register_release_candidate.bctch1.bctch1'}
    GeneratedJob{name='restart-nodes-on-failure'}
    GeneratedJob{name='retrigger-jobs-with-scm-on-failure'}
    GeneratedJob{name='runCommandOnAgent'}
    GeneratedJob{name='store.baseline.bctch1.build'}
    GeneratedJob{name='sync.secrets.configurations'}
    GeneratedJob{name='task13.bfdata.server'}
    GeneratedJob{name='task13.bfdata.win64'}
    GeneratedJob{name='task13.bilbo.register-bfdata-dronebuild'}
    GeneratedJob{name='task13.code.check'}
    GeneratedJob{name='task13.code.copy-to-filer'}
    GeneratedJob{name='task13.code.linux64server.final'}
    GeneratedJob{name='task13.code.linux64server.release'}
    GeneratedJob{name='task13.code.p4counterupdater'}
    GeneratedJob{name='task13.code.start'}
    GeneratedJob{name='task13.code.tool.release'}
    GeneratedJob{name='task13.code.win64game.final'}
    GeneratedJob{name='task13.code.win64game.performance'}
    GeneratedJob{name='task13.code.win64game.release'}
    GeneratedJob{name='task13.code.win64server.final'}
    GeneratedJob{name='task13.code.win64server.release'}
    GeneratedJob{name='task13.data.p4cleancounterupdater'}
    GeneratedJob{name='task13.data.p4counterupdater'}
    GeneratedJob{name='task13.data.start'}
    GeneratedJob{name='task13.frosty.bfdata.linuxserver.files.ww.final'}
    GeneratedJob{name='task13.frosty.bfdata.linuxserver.files.ww.release'}
    GeneratedJob{name='task13.frosty.bfdata.server.files.ww.final'}
    GeneratedJob{name='task13.frosty.bfdata.server.files.ww.release'}
    GeneratedJob{name='task13.frosty.bfdata.win64.files.ww.final'}
    GeneratedJob{name='task13.frosty.bfdata.win64.files.ww.performance'}
    GeneratedJob{name='task13.frosty.bfdata.win64.files.ww.release'}
    GeneratedJob{name='task13.frosty.p4counterupdater'}
    GeneratedJob{name='task13.frosty.start'}
    GeneratedJob{name='task13.shift.offsite_basic_drone_shifter.start'}
    GeneratedJob{name='task13.shift.offsite_basic_drone_shifter.upload'}
    GeneratedJob{name='task13.shift.upload'}
    GeneratedJob{name='task13.spin.linuxserver.files.final.ww'}
    GeneratedJob{name='task13.symbolStoreUpload'}
    GeneratedJob{name='task2.bfdata.linux64'}
    GeneratedJob{name='task2.bfdata.ps5'}
    GeneratedJob{name='task2.bfdata.server'}
    GeneratedJob{name='task2.bfdata.validate-frosted'}
    GeneratedJob{name='task2.bfdata.win64'}
    GeneratedJob{name='task2.bfdata.xbsx'}
    GeneratedJob{name='task2.bilbo.move.linux64server.final.RippleEffect'}
    GeneratedJob{name='task2.bilbo.move.ps5.final.RippleEffect'}
    GeneratedJob{name='task2.bilbo.move.ps5.performance.RippleEffect'}
    GeneratedJob{name='task2.bilbo.move.ps5.release.RippleEffect'}
    GeneratedJob{name='task2.bilbo.move.RippleEffect.start'}
    GeneratedJob{name='task2.bilbo.move.tool.release.RippleEffect'}
    GeneratedJob{name='task2.bilbo.move.win64game.final.RippleEffect'}
    GeneratedJob{name='task2.bilbo.move.win64game.performance.RippleEffect'}
    GeneratedJob{name='task2.bilbo.move.win64game.release.RippleEffect'}
    GeneratedJob{name='task2.bilbo.move.win64server.final.RippleEffect'}
    GeneratedJob{name='task2.bilbo.move.xbsx.final.RippleEffect'}
    GeneratedJob{name='task2.bilbo.move.xbsx.performance.RippleEffect'}
    GeneratedJob{name='task2.bilbo.move.xbsx.release.RippleEffect'}
    GeneratedJob{name='task2.bilbo.register-bfdata-dronebuild'}
    GeneratedJob{name='task2.bilbo.register.local'}
    GeneratedJob{name='task2.bilbo.register.remote.RippleEffect'}
    GeneratedJob{name='task2.code.check'}
    GeneratedJob{name='task2.code.copy-to-filer'}
    GeneratedJob{name='task2.code.linux64server.final'}
    GeneratedJob{name='task2.code.nomaster.ps5.final'}
    GeneratedJob{name='task2.code.nomaster.start'}
    GeneratedJob{name='task2.code.nomaster.tool.release'}
    GeneratedJob{name='task2.code.p4counterupdater'}
    GeneratedJob{name='task2.code.ps5.final'}
    GeneratedJob{name='task2.code.ps5.performance'}
    GeneratedJob{name='task2.code.ps5.release'}
    GeneratedJob{name='task2.code.ps5.retail'}
    GeneratedJob{name='task2.code.start'}
    GeneratedJob{name='task2.code.tool.release'}
    GeneratedJob{name='task2.code.win64game.final'}
    GeneratedJob{name='task2.code.win64game.performance'}
    GeneratedJob{name='task2.code.win64game.release'}
    GeneratedJob{name='task2.code.win64game.retail'}
    GeneratedJob{name='task2.code.win64server.final'}
    GeneratedJob{name='task2.code.xbsx.final'}
    GeneratedJob{name='task2.code.xbsx.performance'}
    GeneratedJob{name='task2.code.xbsx.release'}
    GeneratedJob{name='task2.code.xbsx.retail'}
    GeneratedJob{name='task2.data.p4cleancounterupdater'}
    GeneratedJob{name='task2.data.p4counterupdater'}
    GeneratedJob{name='task2.data.start'}
    GeneratedJob{name='task2.frosty.bfdata.linuxserver.digital.ww.final'}
    GeneratedJob{name='task2.frosty.bfdata.ps5.digital.dev.final'}
    GeneratedJob{name='task2.frosty.bfdata.server.files.ww.final'}
    GeneratedJob{name='task2.frosty.bfdata.win64.files.ww.final'}
    GeneratedJob{name='task2.frosty.bfdata.xbsx.digital.ww.final'}
    GeneratedJob{name='task2.frosty.p4counterupdater'}
    GeneratedJob{name='task2.frosty.start'}
    GeneratedJob{name='task2.gametool.drone'}
    GeneratedJob{name='task2.gametool.fbenv'}
    GeneratedJob{name='task2.gametool.framework'}
    GeneratedJob{name='task2.gametool.frostbiteDatabaseUpgrader'}
    GeneratedJob{name='task2.gametool.frostyisotool'}
    GeneratedJob{name='task2.gametool.icepick'}
    GeneratedJob{name='task2.gametool.start'}
    GeneratedJob{name='task2.pipeline-determinism-test'}
    GeneratedJob{name='task2.pipeline-determinism-test.ps5'}
    GeneratedJob{name='task2.pipeline-determinism-test.start'}
    GeneratedJob{name='task2.symbolStoreUpload'}
    GeneratedJob{name='task4.bfdata.linux64'}
    GeneratedJob{name='task4.bfdata.ps5'}
    GeneratedJob{name='task4.bfdata.server'}
    GeneratedJob{name='task4.bfdata.win64'}
    GeneratedJob{name='task4.bfdata.xbsx'}
    GeneratedJob{name='task4.bilbo.register-bfdata-dronebuild'}
    GeneratedJob{name='task4.code.check'}
    GeneratedJob{name='task4.code.copy-to-filer'}
    GeneratedJob{name='task4.code.linux64server.final'}
    GeneratedJob{name='task4.code.p4counterupdater'}
    GeneratedJob{name='task4.code.ps5.final'}
    GeneratedJob{name='task4.code.ps5.performance'}
    GeneratedJob{name='task4.code.start'}
    GeneratedJob{name='task4.code.tool.release'}
    GeneratedJob{name='task4.code.win64game.final'}
    GeneratedJob{name='task4.code.win64game.performance'}
    GeneratedJob{name='task4.code.win64server.final'}
    GeneratedJob{name='task4.code.xbsx.final'}
    GeneratedJob{name='task4.code.xbsx.performance'}
    GeneratedJob{name='task4.data.p4cleancounterupdater'}
    GeneratedJob{name='task4.data.p4counterupdater'}
    GeneratedJob{name='task4.data.start'}
    GeneratedJob{name='task4.frosty.bfdata.linuxserver.digital.playtest.final'}
    GeneratedJob{name='task4.frosty.bfdata.ps5.files.playtest.performance'}
    GeneratedJob{name='task4.frosty.bfdata.win64.files.playtest.performance'}
    GeneratedJob{name='task4.frosty.bfdata.xbsx.files.playtest.performance'}
    GeneratedJob{name='task4.frosty.p4counterupdater'}
    GeneratedJob{name='task4.frosty.start'}
    GeneratedJob{name='task4.spin.linuxserver.digital.final.playtest'}
    GeneratedJob{name='task4.symbolStoreUpload'}
    GeneratedJob{name='task8.bfdata.linux64'}
    GeneratedJob{name='task8.bfdata.ps5'}
    GeneratedJob{name='task8.bfdata.server'}
    GeneratedJob{name='task8.bfdata.win64'}
    GeneratedJob{name='task8.bfdata.xbsx'}
    GeneratedJob{name='task8.bilbo.register-bfdata-dronebuild'}
    GeneratedJob{name='task8.code.check'}
    GeneratedJob{name='task8.code.copy-to-filer'}
    GeneratedJob{name='task8.code.linux64server.final'}
    GeneratedJob{name='task8.code.p4counterupdater'}
    GeneratedJob{name='task8.code.ps5.final'}
    GeneratedJob{name='task8.code.ps5.performance'}
    GeneratedJob{name='task8.code.start'}
    GeneratedJob{name='task8.code.tool.release'}
    GeneratedJob{name='task8.code.win64game.final'}
    GeneratedJob{name='task8.code.win64game.performance'}
    GeneratedJob{name='task8.code.win64server.final'}
    GeneratedJob{name='task8.code.xbsx.final'}
    GeneratedJob{name='task8.code.xbsx.performance'}
    GeneratedJob{name='task8.data.p4cleancounterupdater'}
    GeneratedJob{name='task8.data.p4counterupdater'}
    GeneratedJob{name='task8.data.start'}
    GeneratedJob{name='task8.frosty.bfdata.linuxserver.digital.playtest.final'}
    GeneratedJob{name='task8.frosty.bfdata.ps5.files.playtest.performance'}
    GeneratedJob{name='task8.frosty.bfdata.win64.files.playtest.performance'}
    GeneratedJob{name='task8.frosty.bfdata.xbsx.files.playtest.performance'}
    GeneratedJob{name='task8.frosty.p4counterupdater'}
    GeneratedJob{name='task8.frosty.start'}
    GeneratedJob{name='task8.shift.start'}
    GeneratedJob{name='task8.shift.upload'}
    GeneratedJob{name='task8.spin.linuxserver.digital.final.playtest'}
    GeneratedJob{name='task8.symbolStoreUpload'}
    GeneratedJob{name='trunk-content-dev.autotest-to-integration.code'}
    GeneratedJob{name='trunk-content-dev.integrate-upgrade-to.CH1-to-trunk'}
    GeneratedJob{name='trunk-content-dev.integrate-upgrade-to.CH1-to-trunk.start'}
    GeneratedJob{name='vault.bctch1.bctch1'}
    GeneratedJob{name='vault.bctch1.bctch1.build'}
    GeneratedJob{name='workspace-deletion-bctch1'}
Existing views:
    GeneratedView{name='2024_1_dev-bf-to-CH1'}
    GeneratedView{name='CH1-SP-content-dev'}
    GeneratedView{name='CH1-SP-content-dev-disc-build'}
    GeneratedView{name='CH1-SP-content-dev-first-patch'}
    GeneratedView{name='CH1-code-dev'}
    GeneratedView{name='CH1-content-dev'}
    GeneratedView{name='CH1-content-dev-C1S2B1'}
    GeneratedView{name='CH1-content-dev-cache-losangeles'}
    GeneratedView{name='CH1-content-dev-clean'}
    GeneratedView{name='CH1-content-dev-disc-build'}
    GeneratedView{name='CH1-content-dev-first-patch'}
    GeneratedView{name='CH1-content-dev-metrics'}
    GeneratedView{name='CH1-content-dev-sanitizers'}
    GeneratedView{name='CH1-marketing-dev'}
    GeneratedView{name='CH1-playtest'}
    GeneratedView{name='CH1-playtest-gnt'}
    GeneratedView{name='CH1-playtest-gnt-na'}
    GeneratedView{name='CH1-playtest-san'}
    GeneratedView{name='CH1-playtest-san-s2'}
    GeneratedView{name='CH1-playtest-sp'}
    GeneratedView{name='CH1-playtest-stage'}
    GeneratedView{name='CH1-to-trunk'}
    GeneratedView{name='Integrations'}
    GeneratedView{name='Integrations Dashboard'}
    GeneratedView{name='Maintenance'}
    GeneratedView{name='bf-anticheat'}
    GeneratedView{name='koala-code'}
    GeneratedView{name='koala-content'}
    GeneratedView{name='koala-onetrunk'}
    GeneratedView{name='task13'}
    GeneratedView{name='task2'}
    GeneratedView{name='task4'}
    GeneratedView{name='task8'}
Run condition [Always] enabling perform for step [[Execute shell]]
[seed] $ /bin/sh -xe /tmp/jenkins17341619124690026163.sh
+ cp -rf resources/email-templates/ /var/jenkins_home/
[Slack Notifications] found #2034 as previous completed, non-aborted build
Triggering a new build of jenkins.cleanup

Finished: SUCCESS
